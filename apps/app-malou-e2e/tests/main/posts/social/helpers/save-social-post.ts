import { expect } from ':baseTest';
import { Page } from '@playwright/test';

export async function saveSocialPost({ page, status }: { page: Page; status: 'now' | 'schedule' | 'draft' }) {
    await expect(async () => {
        const menuBtn = page.getByTestId('upsert-social-post-save-dropdown-menu-btn').first();
        await menuBtn.click();

        const btn = page.getByTestId(`upsert-social-post-save-dropdown-btn-${status}`).first();
        await btn.click();

        const saveBtn = page.getByTestId('upsert-social-post-save-dropdown-btn').first();
        await saveBtn.click();
    }).toPass();
}
