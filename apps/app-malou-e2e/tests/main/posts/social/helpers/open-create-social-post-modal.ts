import { Page } from '@playwright/test';
import { expect } from 'baseTest';

export async function openCreateSocialPostModal(page: Page, isReel = false) {
    await expect(async () => {
        await page.getByTestId('social-post-open-create-post-modal-btn').click();

        if (isReel) {
            await page.getByTestId('social_post_menu_open_create_reel_modal_btn_v2').click();
        } else {
            await page.getByTestId('social_post_menu_open_create_post_modal_btn_v2').click();
        }

        await page.waitForResponse(/posts\/v2/);

        const modal = page.locator('app-upsert-social-post-modal');

        // we target close-upsert-social-post-btn because it's one of the first button to be visible
        // it ensures that the modal is loaded
        await expect(modal.getByTestId('close-upsert-social-post-btn')).toBeVisible();
    }).toPass();
}
