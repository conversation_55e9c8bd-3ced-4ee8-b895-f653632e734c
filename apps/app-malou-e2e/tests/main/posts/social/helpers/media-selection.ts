import { expect } from ':baseTest';
import { Page } from '@playwright/test';

export async function addMediaToSocialPost({ page, folderName, mediaCount = 1 }: { page: Page; folderName?: string; mediaCount?: number }) {
    await expect(async () => {
        await page.getByTestId('add-first-media-menu-btn').click();
        await page.getByTestId('add-media-from-computer-btn').click();

        const modalPicker = page.locator('app-media-picker-modal');
        if (folderName) {
            await modalPicker.getByText(folderName).click();
        }

        // used to wait for weird loading image refresh
        await page.waitForTimeout(2000);

        const imgs = await modalPicker.getByRole('img').all();
        const videos = await modalPicker.locator('video').all();

        const imgCount = imgs.length;
        const videoCount = videos.length;

        if (imgCount + videoCount < mediaCount) {
            throw new Error(`Not enough media in folder ${folderName}, expected ${mediaCount} but got ${imgCount + videoCount}`);
        }

        for (let i = 0; i < mediaCount; i++) {
            if (i >= imgCount) {
                await videos[i - imgs.length]!.click();
                continue;
            }
            await imgs[i]!.click();
        }

        await modalPicker.getByTestId('media-picker-add-media-btn').click();
    }).toPass();
}
