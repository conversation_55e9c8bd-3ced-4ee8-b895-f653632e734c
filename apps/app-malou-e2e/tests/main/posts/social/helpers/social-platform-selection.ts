import { Locator, <PERSON> } from '@playwright/test';
import { expect } from 'baseTest';

import { PlatformKey } from '@malou-io/package-utils';

export async function selectPlatformInSocialPostModal(page: Page, platformKey: PlatformKey) {
    await expect(async () => {
        const slideToggle = page.getByTestId(`upsert_social_post_modal_select_platform_btn_${platformKey}`);
        const isSelected = await isPlatformSelected(slideToggle);
        if (isSelected) {
            return;
        }
        await page.getByTestId(`upsert_social_post_modal_select_platform_btn_${platformKey}`).click();
    }).toPass();
}

export async function unselectPlatformInSocialPostModal(page: Page, platformKey: PlatformKey) {
    await expect(async () => {
        const slideToggle = page.getByTestId(`upsert_social_post_modal_select_platform_btn_${platformKey}`);
        const isSelected = await isPlatformSelected(slideToggle);
        if (!isSelected) {
            return;
        }
        await page.getByTestId(`upsert_social_post_modal_select_platform_btn_${platformKey}`).click();
    }).toPass();
}

async function isPlatformSelected(slideToggle: Locator) {
    const classes = await slideToggle.getAttribute('class');
    return classes?.includes('mat-mdc-slide-toggle-checked');
}
