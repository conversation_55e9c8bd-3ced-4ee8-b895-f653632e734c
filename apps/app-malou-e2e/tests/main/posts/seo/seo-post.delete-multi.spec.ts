import { expect, it } from ':baseTest';

import { generateRandomString } from ':helpers';
import { openSeoPostModal } from ':shared/open-seo-post';

it('should create and delete an seo post', async ({ page }) => {
    const POST_TEXT_CONTENT = `Test post ${generateRandomString(8)}`;
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/seo/posts/list`);

    await openSeoPostModal(page);

    await page.waitForResponse(/prepare/);

    const modal = page.locator('app-new-post-modal');

    await modal.getByTestId('seo-post-caption-input').fill(POST_TEXT_CONTENT);

    await modal.getByTestId('draft-btn').click();

    await modal.getByTestId('social-post-save-btn').click();

    await page.waitForResponse(/posts\/restaurants/);

    const newPostLocator = page.locator('app-single-post', { hasText: POST_TEXT_CONTENT });

    await expect(newPostLocator).toHaveCount(1);

    const newPost = newPostLocator.first();

    const checkbox = newPost.locator('input[type="checkbox"]');

    await checkbox.click();

    await page.getByTestId('delete-seo-posts-btn').click();

    const confirmDeleteModal = page.locator('app-malou-dialog');

    await confirmDeleteModal.locator('button', { hasText: 'supprimer' }).click();

    await page.waitForResponse(/delete/);

    const postThatShouldHaveBeenDeleted = page.locator('app-single-post', { hasText: POST_TEXT_CONTENT });

    await expect(postThatShouldHaveBeenDeleted).toHaveCount(0);
});
