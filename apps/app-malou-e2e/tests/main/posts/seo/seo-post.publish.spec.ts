import { expect, it } from 'baseTest';

import { PostSource } from '@malou-io/package-utils';

import { appendRandomCharacterToTextAtRandomPosition, generateRandomSentence } from ':helpers';
import { importMediaFromGallery } from ':shared/import-media-from-gallery';
import { openSeoPostModal } from ':shared/open-seo-post';

it('should publish single post', async ({ page }) => {
    const POST_TEXT_CONTENT = appendRandomCharacterToTextAtRandomPosition(generateRandomSentence());
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/seo/posts/list`);

    await openSeoPostModal(page);

    const modal = await page.locator('app-new-post-modal');

    await importMediaFromGallery({ page, folderName: 'seo', fromModal: PostSource.SEO });

    await expect(await modal.getByTestId('seo-post-picture-name-input')).not.toBeEmpty();

    await modal.getByTestId('seo-post-type-input').click();

    await page.locator('mat-option').first().click();

    await modal.getByTestId('seo-post-caption-input').fill(POST_TEXT_CONTENT);
    await modal.getByTestId('seo-post-call-to-action-input').click();

    await page.locator('mat-option').nth(2).click();

    await modal.getByTestId('seo-post-url-call-to-action-input').fill('https://www.malou.io');

    await modal.getByTestId('now-btn').click();

    await page.waitForResponse(/prepare/i);

    await modal.getByTestId('social-post-save-btn').click();

    // we don't check for status 'published' because probably one day we will be ban from gmb because its a fake restaurant
    await page.waitForResponse(/by_binding_id/i);
});
