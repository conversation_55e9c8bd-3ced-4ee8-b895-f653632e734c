import { expect, it } from 'baseTest';

import { openSeoPostModal } from ':shared/open-seo-post';

// Will run on Le Nina

it('should duplicate an seo post', async ({ page }) => {
    const POST_TEXT_CONTENT = 'Lorem ipsum';
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/seo/posts/list`);

    await openSeoPostModal(page);

    const modal = page.locator('app-new-post-modal');

    await modal.getByTestId('seo-post-caption-input').fill(POST_TEXT_CONTENT);

    await modal.getByTestId('draft-btn').click();

    await modal.getByTestId('social-post-save-btn').click();

    await page.waitForResponse(/posts\/restaurants\//i);

    const newPost = page.locator('app-single-post', { hasText: POST_TEXT_CONTENT }).first();

    const checkbox = newPost.locator('input[type="checkbox"]');

    await checkbox.click();

    await page.getByTestId('duplicate-seo-post-open-modal-btn').click();
    await page.getByTestId('duplicate-seo-posts-in-seo-btn').click();

    const postList = await page.locator('app-single-post').all();
    expect(postList.length).toBeGreaterThanOrEqual(2);
    for (const post of postList) {
        const currentCheckbox = post.locator('input[type="checkbox"]');
        await expect(currentCheckbox).not.toBeChecked();
    }
});
