import { expect, it } from 'baseTest';
import { createTestUser, deleteTestUser } from 'tests/main/settings/users/utils';

import { ApplicationLanguage } from '@malou-io/package-utils';

it.describe('New User Modal - Existing User Language', () => {
    const testUserEmail = `test-user-${Date.now()}@example.com`;

    it('should preserve existing user language and disable language dropdown', async ({ page }) => {
        // Step 1: Create a random user in the database with Italian language
        await createTestUser({
            email: testUserEmail,
            language: ApplicationLanguage.IT,
        });

        // Step 2: Login as admin and navigate to Le Nino Traiteur settings/roles
        const restaurantId = process.env.RESTAURANT_ID_LE_NINO;

        // Step 3: Go to the settings/roles page
        await page.goto(`/restaurants/${restaurantId}/settings/roles`, { waitUntil: 'commit' });
        await page.waitForSelector('mat-table', { state: 'visible' });

        // Step 4: Click on the "Add User" button to open the modal
        await page.getByTestId('add-user-btn').click();

        // Wait for the modal to appear
        const modal = page.locator('app-new-user-modal');
        await expect(modal).toBeVisible();

        // Step 5: Click on the email input and type the full email of the created user
        const emailInput = page.getByTestId('new-user-email-select-0');

        // Click on the input field within the select component
        // const emailInput = emailSelect.locator('input');
        await emailInput.click();
        await emailInput.fill(testUserEmail);

        // Press Enter to trigger the itemBuilder
        await emailInput.press('Enter');

        // Step 6: Wait for the itemBuilder to process the email and populate the user data
        await page.waitForTimeout(2000); // Give time for the async itemBuilder to complete

        // Step 7: Check that in the language dropdown, Italian is selected
        const languageDropdown = page.locator('app-select-languages');

        // Check if the language dropdown shows Italian flag or text
        const italianFlag = languageDropdown.locator('img[src*="it.png"]');

        await expect(italianFlag).toBeVisible();

        // Step 9: Try to click on the language dropdown to verify it doesn't open
        await languageDropdown.click();

        // Wait a moment to see if any dropdown options appear
        await page.waitForTimeout(500);

        // Check that no dropdown options are visible (because it's disabled)
        const dropdownOptions = page.locator('.mat-option, .mat-autocomplete-panel');
        await expect(dropdownOptions).not.toBeVisible();

        // Step 10: Close the modal by clicking the close button (X)
        await page.getByTestId('new-user-modal-close-btn').click();

        // Step 11: Wait for the close-without-saving modal to appear
        const saveModal = page.locator('app-close-without-saving-modal');
        await expect(saveModal).toBeVisible();

        // Step 12: Click the confirm button on the close-without-saving modal
        await page.getByTestId('close-without-saving-confirm').click();

        // Step 13: Verify both modals are closed
        await expect(modal).not.toBeVisible();
        await expect(saveModal).not.toBeVisible();
    });

    // Cleanup: Remove the test user from the database
    it.afterAll(async () => {
        if (testUserEmail) {
            await deleteTestUser(testUserEmail);
        }
    });
});
