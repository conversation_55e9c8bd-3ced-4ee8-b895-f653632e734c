import { Locator, Page } from '@playwright/test';
import { expect, it } from 'baseTest';
import { resetUserToCaslRole } from 'tests/main/settings/users/utils';

import { CaslRole } from '@malou-io/package-utils';

import { E2E_ADMIN_USER, E2E_BASIC_USER } from ':constants';
import { login } from ':shared/login';
import { logout } from ':shared/logout';

it.describe.configure({ mode: 'serial' });

async function getRoleSelectorForUser({ page, userEmail }: { page: Page; userEmail: string }): Promise<Locator | null> {
    const rowSelector = '.mat-mdc-row';
    const rowCount = await page.locator(rowSelector).count();
    for (let i = 0; i < rowCount; i++) {
        const row = page.locator(rowSelector).nth(i);
        const email = await row.locator('.mat-column-email').first().getByTestId('roles-manager-email').textContent();
        if (email?.trim() === userEmail) {
            return row.locator('.mat-column-urRole').first().getByRole('combobox');
        }
    }

    return null;
}

it('should not be able to change CASL roles if basic user is not an owner', async ({ page }) => {
    const restaurantId = process.env.RESTAURANT_ID_LE_NINO;
    await resetUserToCaslRole({ userEmail: E2E_BASIC_USER.email, restaurantId, caslRole: CaslRole.GUEST });
    await page.goto(`/restaurants/${restaurantId}/dashboard`, { waitUntil: 'commit' });
    await logout({ page });
    await page.waitForURL(/login/i);
    await login({ page, email: E2E_BASIC_USER.email, password: E2E_BASIC_USER.password! });

    await page.goto(`/restaurants/${restaurantId}/settings/roles`, { waitUntil: 'commit' });
    await page.waitForSelector('mat-table', { state: 'visible' });

    const roleSelector = await getRoleSelectorForUser({ page, userEmail: E2E_BASIC_USER.email });
    if (!roleSelector) {
        expect(roleSelector).not.toBeNull();
    } else {
        await expect(roleSelector).toBeDisabled();
    }
});

it('should be able to change CASL roles if the user an admin', async ({ page }) => {
    const restaurantId = process.env.RESTAURANT_ID_LE_NINO;
    await page.goto(`/restaurants/${restaurantId}/dashboard`, { waitUntil: 'commit' });
    await logout({ page });
    await page.waitForURL(/login/i);
    await login({ page, email: E2E_ADMIN_USER.email, password: E2E_ADMIN_USER.password! });

    await page.goto(`/restaurants/${restaurantId}/settings/roles`, { waitUntil: 'commit' });
    await page.waitForResponse(async (response) => {
        return response.url().includes(`/users/restaurants/${restaurantId}`) && response.status() === 200;
    });

    const roleSelector = await getRoleSelectorForUser({ page, userEmail: E2E_BASIC_USER.email });
    if (!roleSelector) {
        expect(roleSelector).not.toBeNull();
    } else {
        await expect(roleSelector).not.toBeDisabled();
        await roleSelector.click(); // open dropdown
        await page.getByTestId('casl-role-option-owner').click();
    }
});

it('should be able to change CASL roles if the user is basic but an owner', async ({ page }) => {
    const restaurantId = process.env.RESTAURANT_ID_LE_NINO;
    await resetUserToCaslRole({ userEmail: E2E_BASIC_USER.email, restaurantId, caslRole: CaslRole.OWNER });
    await page.goto(`/restaurants/${restaurantId}/dashboard`, { waitUntil: 'commit' });
    await logout({ page });
    await page.waitForURL(/login/i);
    await login({ page, email: E2E_BASIC_USER.email, password: E2E_BASIC_USER.password! });

    await page.goto(`/restaurants/${restaurantId}/settings/roles`, { waitUntil: 'commit' });
    await page.waitForSelector('mat-table', { state: 'visible' });

    const roleSelector = await getRoleSelectorForUser({ page, userEmail: E2E_BASIC_USER.email });
    if (!roleSelector) {
        expect(roleSelector).not.toBeNull();
        return;
    }

    await expect(roleSelector).not.toBeDisabled();
    await roleSelector.click(); // open dropdown
    await page.getByTestId('casl-role-option-guest').click(); // It opens a dialog to confirm the change
    await page.locator('app-malou-dialog').first().getByTestId('malou-dialog-primary-btn').click();
    await page.waitForTimeout(500);

    await page.reload();
    await page.waitForSelector('mat-table', { state: 'visible' });
    const roleSelectorAfterReload = await getRoleSelectorForUser({ page, userEmail: E2E_BASIC_USER.email });
    if (!roleSelectorAfterReload) {
        expect(roleSelectorAfterReload).not.toBeNull();
        return;
    }
    await expect(roleSelectorAfterReload).toBeDisabled();
});
