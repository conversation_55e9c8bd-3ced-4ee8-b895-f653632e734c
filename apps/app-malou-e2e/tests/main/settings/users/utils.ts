import { UserModel, UserRestaurantModel } from '@malou-io/package-models';
import { ApplicationLanguage, Role } from '@malou-io/package-utils';

export async function resetUserToCaslRole({
    userEmail,
    restaurantId,
    caslRole,
}: {
    userEmail: string;
    restaurantId: string;
    caslRole: string;
}) {
    const user = await UserModel.findOne({ email: userEmail });
    if (!user) {
        throw new Error(`User with email ${userEmail} not found`);
    }
    const userRestaurant = await UserRestaurantModel.findOne({ restaurantId, userId: user._id });
    if (!userRestaurant) {
        throw new Error(`UserRestaurant not found`);
    }
    await userRestaurant.updateOne({ $set: { caslRole } });
}

export async function createTestUser({
    email,
    language,
    organizationId,
}: {
    email: string;
    language: ApplicationLanguage;
    organizationId?: string;
}): Promise<string> {
    const userData = {
        email,
        name: 'Test',
        lastname: 'User',
        defaultLanguage: language,
        role: Role.MALOU_BASIC,
        verified: true,
        password: '$2a$10$o8rbfxbiOm.tcH0oxgV5nesMK4uP.618wHURQyqdHXsldUCyNuday', // hashed 'password'
        organizationIds: organizationId ? [organizationId] : [],
    };

    const user = new UserModel(userData);
    await user.save();
    return user._id.toString();
}

export async function deleteTestUser(email: string): Promise<void> {
    await UserModel.deleteOne({ email });
}
