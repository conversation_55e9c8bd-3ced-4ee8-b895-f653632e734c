import { Page } from '@playwright/test';
import { expect } from 'baseTest';

export enum ReviewReplyAutomationType {
    WITH_COMMENT = 'with-comment',
    WITHOUT_COMMENT = 'without-comment',
}

export async function openEditReviewReplyModal(page: Page, type: ReviewReplyAutomationType) {
    await expect(async () => {
        let automationCard = null;

        if (type === ReviewReplyAutomationType.WITH_COMMENT) {
            automationCard = page.locator('app-automation-card').first();
        } else {
            automationCard = page.locator('app-automation-card').last();
        }

        const button = automationCard.locator('button').first();

        await button.click();

        const modal = page.locator('app-edit-review-reply-automations-modal');

        await page.waitForTimeout(1000);

        const slideToggles = await modal.locator('mat-slide-toggle').count();

        expect(slideToggles).toBe(4);
    }).toPass();
}
