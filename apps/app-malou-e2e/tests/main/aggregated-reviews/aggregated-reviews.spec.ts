import { expect, it } from 'baseTest';

it('should open a gmb review modal', async ({ page }) => {
    await page.goto('/groups/reputation/reviews');

    const reviewText = 'Les crevettes etaient tres bonnes !';

    await expect(page.locator('app-review-preview').first()).toBeVisible({ timeout: 20_000 });

    await page.getByTestId('reviews-filters-btn').click();

    const menu = await page.locator('.mat-mdc-menu-panel').first();

    const resetFilterBtn = await menu.locator('button').first();

    // check if reset filter button is disabled
    if ((await resetFilterBtn.getAttribute('disabled')) === null) {
        await resetFilterBtn.click();
    }

    await page.locator('body').click();
    await page.locator('app-reviews-header').getByTestId('reviews-search-input').first().fill(reviewText);
    await page.waitForResponse(/reviews\/v2/);

    const reviewBox = await page.locator('app-review-preview').filter({ hasText: reviewText }).first();

    await expect(reviewBox).toBeVisible();
});
