import { getRestaurantsIds } from 'setup/utils';

import { ReviewModel, TranslationsModel } from '@malou-io/package-models';

import { REVIEW_SOCIAL_IDS } from ':constants';

async function setupReviews() {
    const { leNinaRestaurantId } = getRestaurantsIds();

    const facebookEnglishReviewSocialId = REVIEW_SOCIAL_IDS.FACEBOOK_ENGLISH_REVIEW;

    const facebookEnglishReview = await ReviewModel.findOne({ socialId: facebookEnglishReviewSocialId, restaurantId: leNinaRestaurantId });
    if (facebookEnglishReview) {
        const translationsId = facebookEnglishReview.translationsId?.toString();
        facebookEnglishReview.set({
            translationsId: null,
        });
        await facebookEnglishReview.save();
        await TranslationsModel.deleteOne({ _id: translationsId });
    }

    const archiveReviewId = REVIEW_SOCIAL_IDS.ARCHIVE;

    const archiveReview = await ReviewModel.findOne({ restaurantId: leNinaRestaurantId, socialId: archiveReviewId });
    if (archiveReview) {
        archiveReview.set({
            archived: false,
        });
        await archiveReview.save();
    }
}

export default setupReviews;
