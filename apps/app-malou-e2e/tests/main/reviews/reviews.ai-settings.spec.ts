import { expect, it } from 'baseTest';

it.describe.configure({ mode: 'serial' });

const NEW_RESTAU_NAME = 'Le nina 94';
const SIGNATURE = 'La Nina super équipe';
const CLIENT_NAME = /gestion|clients|client|malou/i;
const gaugeCssKeywordClass = /step--orange|step--green/i;

it('should setup AI settings', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/settings/ai`);

    await page.getByTestId('general-ai-settings-restaurant-name-input').fill(NEW_RESTAU_NAME);

    await page.getByTestId('general-ai-settings-language-select').click();

    await page.locator('mat-option').nth(0).click();

    await page.getByTestId('restaurants-review-ai-settings-edit-btn').click();

    await page.getByTestId('general-ai-settings-customer-name-input').click();

    await page.locator('mat-option').nth(1).click();

    await page.getByTestId('general-ai-settings-signature-input').fill(SIGNATURE);

    await page.getByTestId('general-ai-settings-formal-input').click();

    await page.getByTestId('general-ai-settings-save-btn').click();
});

it('should be able to use custom settings', async ({ page }) => {
    await page.goto(
        `/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews?reviewId=${process.env.GESTION_CLIENTS_GMB_REVIEW_ID}`
    );
    await page.waitForTimeout(5000);

    await page.getByTestId('hide-reply-section-button').click();

    await page.getByTestId('answer-review-text-area-input').fill('');

    await page.getByTestId('answer-review-ask-ai-btn').click();

    await Promise.all([
        page.waitForResponse(async (response) => {
            return response.url().includes('/answer-review') && response.status() === 200;
        }),

        page.waitForResponse(async (response) => {
            return response.url().includes('/score') && response.status() === 200;
        }),
    ]);

    await page.evaluate(() => {
        document.querySelector('app-answer-review-modal .malou-dialog__body').scroll(0, 1000);
    });

    const inputValue = await page.getByTestId('answer-review-text-area-input').inputValue();

    expect(inputValue).toMatch(CLIENT_NAME);
    expect(inputValue).toContain(SIGNATURE);

    // check only first element has the class 'red'
    const gauge = await page.getByTestId('keywords-score-brick-container').locator('app-score-gauge');
    await expect(gauge.getByTestId('score-gauge-step').nth(0)).toHaveClass(gaugeCssKeywordClass);
    await expect(gauge.getByTestId('score-gauge-step').nth(1)).toHaveClass(gaugeCssKeywordClass);
});
