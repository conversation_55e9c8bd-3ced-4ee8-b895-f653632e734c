import { expect, it } from 'baseTest';

import { selectMaxDateRangeReviewFilter } from ':shared/select-max-date-range-review-filter';

it('sould open a review modal from review list', async ({ page }) => {
    const reviewText = 'Les crevettes etaient tres bonnes';

    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews`);

    await selectMaxDateRangeReviewFilter({ page });

    const inputReviewsSearch = await page.getByTestId('main-reviews-header-search').getByTestId('reviews-search-input');

    await inputReviewsSearch.click();
    await inputReviewsSearch.fill(reviewText);
    await page.waitForResponse(/reviews\/v2/);

    const reviewBox = await page.locator('app-review-preview').filter({ hasText: reviewText }).first();

    const buttonEditOrReply = reviewBox.getByTestId('review-preview-reply-btn').or(reviewBox.getByTestId('review-preview-edit-reply-btn'));

    await buttonEditOrReply.click();

    await page.waitForTimeout(5000);
    await expect(await page.locator('app-answer-review-modal').first()).toBeVisible();
});

it('should reply Gmb review manually', async ({ page }) => {
    const replyText = 'Merci pour votre commentaire, vos retours sont très importants pour nous. A bientot';
    const reviewText = 'Les crevettes etaient tres bonnes';
    await page.goto(
        `/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews?reviewId=${process.env.GESTION_CLIENTS_GMB_REVIEW_ID}`
    );
    await page.waitForTimeout(5000);

    await page.getByTestId('hide-reply-section-button').click();

    await page.getByTestId('answer-review-text-area-input').fill(replyText);
    await page.getByTestId('answer-review-reply-btn').click();

    const closeBtn = await page.getByTestId('answer-review-modal-close-btn');

    await closeBtn.click();

    await expect(closeBtn).not.toBeVisible();

    const inputReviewsSearch = await page.getByTestId('main-reviews-header-search').getByTestId('reviews-search-input');

    await inputReviewsSearch.click();
    await inputReviewsSearch.fill(reviewText);
    await expect(await page.locator('app-review-preview').filter({ hasText: reviewText }).first()).toContainText(replyText);
});

it('should reply to a Gmb review 4 stars with template and check if keyword score updating and keywords used are checked', async ({
    page,
}) => {
    await page.goto(
        `/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews?reviewId=${process.env.GESTION_CLIENTS_GMB_REVIEW_ID}`
    );

    await page.waitForSelector('app-answer-review', { state: 'visible' });

    await expect(page.locator('app-answer-review-modal')).toBeVisible();

    await page.getByTestId('hide-reply-section-button').click();

    await page.waitForSelector('app-answer-review-keyword-score-gauge', { state: 'visible' });

    await page.waitForResponse(async (response) => {
        return response.url().includes('/score') && response.status() === 200;
    });

    const modalRightSide = page.locator('app-answer-review');

    await modalRightSide.getByTestId('answer-review-see-templates-btn').click();

    await modalRightSide.getByTestId('answer-review-templates-autocomplete-input').click();

    const option = await page.locator('mat-option').filter({ hasText: 'modele 4 etoiles' });

    await expect(option.locator('app-star-with-text-chip')).toContainText('4');

    await option.click();

    await page.waitForResponse(async (response) => {
        return response.url().includes('/score') && response.status() === 200;
    });

    await page.evaluate(() => {
        document.querySelector('app-answer-review-modal .malou-dialog__body').scroll(0, 1000);
    });

    const inputValue = await page.getByTestId('answer-review-text-area-input').inputValue();

    const keyword1 = 'traiteur';
    const keyword2 = 'restaurant';

    expect(inputValue).toContain(`Nous sommes ravis que votre expérience de ${keyword1} ${keyword2} vous ait convaincu`);

    const gauge = page.getByTestId('keywords-score-brick-container').locator('app-score-gauge');

    // check that it's an average to low score
    await expect(gauge.getByTestId('score-gauge-step').nth(0)).toHaveClass(/step--orange|step--red/);
    await expect(gauge.getByTestId('score-gauge-step').nth(4)).toHaveClass(/step--white/);

    const keywordsChecked = await page.getByTestId('keywords-score-brick').locator('mat-icon[data-mat-icon-name="check"]').count();
    expect(keywordsChecked).toEqual(2);
});
