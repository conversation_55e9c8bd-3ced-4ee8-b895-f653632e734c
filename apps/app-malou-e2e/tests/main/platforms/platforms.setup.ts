import { getRestaurantsIds } from 'setup/utils';

import { PlatformModel, PostModel } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

async function setupPlatforms() {
    const { leNinoRestaurantId } = getRestaurantsIds();

    await PostModel.deleteMany({ restaurantId: leNinoRestaurantId, published: { $ne: PostPublicationStatus.PENDING } });

    await Promise.all(Object.values(PlatformKey).map((key) => PlatformModel.deleteOne({ restaurantId: leNinoRestaurantId, key })));
}

export default setupPlatforms;
