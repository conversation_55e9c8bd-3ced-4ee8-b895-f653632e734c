import { expect, it } from 'baseTest';

import { ONE_MINUTE } from ':constants';
import { generateRandomString } from ':helpers';

it.setTimeout(ONE_MINUTE * 2);

it.describe.configure({ mode: 'serial' });

it('should fill the generator form and generate keywords', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINO}/resources/keywords/list`);
    await page.getByTestId('keywords-chevron-down-btn').click();
    await page.getByTestId('keywords-open-edit-form-btn').click();

    const generatorBoxSteps = await page.locator('app-generator');

    // step 1

    const chipText = 'restaurant végétarien';

    // remove chip
    await generatorBoxSteps.locator('.malou-chip--primary').filter({ hasText: chipText }).first().locator('mat-icon').click();
    const textbox1 = generatorBoxSteps.getByTestId('generator-category-input');
    await textbox1.click(); // to open the dropdown
    await textbox1.fill(chipText);

    await page.locator('mat-option').first().click();

    // to close the dropdown
    await generatorBoxSteps.locator('mat-icon[data-mat-icon-name="chevron-down"]').first().click();

    const textbox2 = await generatorBoxSteps.getByTestId('generator-specials-input');
    await textbox2.click();
    await textbox2.fill('locavore');

    await page.locator('mat-option').first().click();

    const nextBtn = await page.getByTestId('generator-next-step-btn');
    await nextBtn.click();

    // step 2
    const location = 'choisy-le-roi';
    const locationInput = await generatorBoxSteps.getByTestId('generator-location-input');
    await locationInput.fill(location);
    await generatorBoxSteps
        .locator('div')
        .filter({ hasText: /choisy-le-roi/i })
        .first()
        .click();
    await nextBtn.click();

    // step 3

    await nextBtn.click();

    // step 4
    await generatorBoxSteps.getByTestId('generator-languages-input').click();
    await page.locator('mat-option').first().click();
    await page.locator('mat-option').nth(1).click();
    await nextBtn.click();

    await expect(await page.getByTestId('keywords-generator-loader')).toBeVisible();

    await page.waitForResponse(
        async (response) => {
            return response.url().includes('/generate/watch') && (await response.text())?.includes('finished');
        },
        { timeout: ONE_MINUTE * 2 }
    );

    await page.getByTestId('keywords-choose-for-me-btn').click();
    await page.getByTestId('keywords-save-selected-btn').click();

    await expect(async () => {
        await expect(await page.getByTestId('keywords-list-nb-keywords')).toContainText('10', { timeout: 10_000 });
    }).toPass();
});

it('should choose keywords', async ({ page }) => {
    await page.goto(`restaurants/${process.env.RESTAURANT_ID_LE_NINO}/resources/keywords/list`);

    await expect(async () => {
        await page.locator('#tracking_keywords_modification').click();
        await page.locator('app-keywords-validation-modal').first();
    }).toPass();

    const ownKeyword = `traiteur ${generateRandomString()}`;

    await page.getByTestId('choose-keywords-select-all-input').click();
    await page.getByTestId('choose-keywords-remove-all-input').click();

    const leftTable = await page.locator('app-unselected-keywords-validation-modal');
    await leftTable.getByTestId('choose-keywords-search-keyword-input').click();
    await leftTable.getByTestId('choose-keywords-search-keyword-input').fill(ownKeyword);

    await page.getByRole('menuitem', { name: ownKeyword }).click();

    await page.waitForResponse(async (response) => {
        return response.url().includes('/keywords/restaurants') && response.status() === 200;
    });

    await leftTable.locator('mat-row').nth(1).click();
    await leftTable.locator('mat-row').nth(2).click();

    await page.getByTestId('choose-keywords-add-selected-keywords-btn').click();
    await page.getByTestId('keywords-save-selected-btn').click();

    await expect(async () => {
        await expect(await page.getByTestId('keywords-list-nb-keywords')).toContainText('3', { timeout: 10_000 });
    }).toPass();
});
