import { getRestaurantsIds } from 'setup/utils';

import { KeywordModel, RestaurantKeywordModel } from '@malou-io/package-models';

async function setupKeywords() {
    const { leNinoRestaurantId } = getRestaurantsIds();

    const leNinoKeywordsCount = await KeywordModel.find({ restaurantId: leNinoRestaurantId }).countDocuments();

    if (leNinoKeywordsCount > 200) {
        throw new Error('Weird number of keywords in leNinoRestaurant, skip deleteMany, please check the data');
    }

    await KeywordModel.deleteMany({
        restaurantId: leNinoRestaurantId,
    });

    const leNinoRestaurantKeywordsCount = await RestaurantKeywordModel.find({ restaurantId: leNinoRestaurantId }).countDocuments();

    if (leNinoRestaurantKeywordsCount > 200) {
        throw new Error('Weird number of restaurant keywords in leNinoRestaurant, skip deleteMany, please check the data');
    }

    await RestaurantKeywordModel.deleteMany({
        restaurantId: leNinoRestaurantId,
    });
}

export default setupKeywords;
