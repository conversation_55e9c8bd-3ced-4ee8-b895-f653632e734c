import { expect, it } from 'baseTest';

import { BusinessCategory } from '@malou-io/package-utils';

import { E2E_USER_REPORTS, ORGANIZATION_NAME } from ':constants';
import { createRestaurant } from ':shared/create-restaurant';
import { login } from ':shared/login';
import { logout } from ':shared/logout';

it.skip('should check if new user has his reports setup', async ({ page }) => {
    await page.goto('/admin/users', { waitUntil: 'commit' });
    await page.waitForResponse(async (response) => {
        return response.url().includes('/users') && response.status() === 200;
    });
    await page.getByTestId('open-create-user').click();
    await page.getByTestId('input-firstname').fill('qareports');
    await page.getByTestId('input-lastname').fill('qa');
    await page.getByTestId('input-email').fill(E2E_USER_REPORTS.email);
    await page.getByTestId('input-password').fill(E2E_USER_REPORTS.password);
    await page.getByTestId('select-organizations').fill(ORGANIZATION_NAME);
    await page.getByTestId('select-organizations').click(); // open dropdown
    await page.getByRole('option', { name: ORGANIZATION_NAME }).click();
    await page.getByTestId('create-user').click();

    await page.waitForResponse(async (response) => {
        return response.url().includes('/new_account') && response.status() === 200;
    });

    await logout({ page });

    await page.waitForURL(/login/i);

    await login({ page, email: E2E_USER_REPORTS.email, password: E2E_USER_REPORTS.password });

    await createRestaurant({ page, restaurantName: 'Le Nina', accountName: E2E_USER_REPORTS.email, type: BusinessCategory.LOCAL_BUSINESS });

    await page.getByTestId('profile').click();
    await page.getByTestId('my-reports').click();

    await page.waitForURL(/reports/i);

    await expect(await page.locator('app-reviews-reports-settings').first()).toBeVisible();

    const selectRestaurantsInputs = await page.locator('app-select-restaurants').all();
    const selectRecipientsInputs = await page.locator('app-select-recipients').all();

    expect(selectRestaurantsInputs).not.toHaveLength(0);
    expect(selectRecipientsInputs).not.toHaveLength(0);

    for (const input of selectRestaurantsInputs) {
        expect(input).toContainText('Le Nina');
    }
    for (const input of selectRecipientsInputs) {
        expect(input).toContainText(E2E_USER_REPORTS.email);
    }
});
