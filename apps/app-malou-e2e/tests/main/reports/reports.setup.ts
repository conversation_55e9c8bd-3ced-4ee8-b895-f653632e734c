import { ReportModel, UserModel } from '@malou-io/package-models';

import { E2E_ADMIN_USER } from ':constants';

async function setupReports() {
    const e2eUser = await UserModel.findOne({ email: E2E_ADMIN_USER.email }, { _id: 1 }, { lean: true });

    if (!e2eUser) {
        throw new Error('User E2E e2eUser not found ! exit...');
    }

    await ReportModel.updateMany({ userId: e2eUser._id }, { $set: { active: true } });
}

export default setupReports;
