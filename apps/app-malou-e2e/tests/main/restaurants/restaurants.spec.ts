import it from '@playwright/test';

import { BusinessCategory } from '@malou-io/package-utils';

import { E2E_GOOGLE_ACCOUNT, getCurrentFacebookInstagramAccount } from ':constants';
import { createRestaurant } from ':shared/create-restaurant';

const currentAccount = getCurrentFacebookInstagramAccount();

it('should create business restaurant', async ({ page }) => {
    await createRestaurant({
        page,
        restaurantName: 'Le Nina',
        type: BusinessCategory.LOCAL_BUSINESS,
        accountName: E2E_GOOGLE_ACCOUNT.email,
    });
});

it('should create brand restaurant', async ({ page }) => {
    await createRestaurant({
        page,
        restaurantName: currentAccount.brandRestaurantName,
        type: BusinessCategory.BRAND,
        accountName: currentAccount.facebookAccountName,
    });
});
