import { expect, it } from 'baseTest';
import { editSocialPostCaption } from 'tests/main/posts/social/helpers/edit-social-post-caption';
import { addMediaToSocialPost } from 'tests/main/posts/social/helpers/media-selection';
import { openCreateSocialPostModal } from 'tests/main/posts/social/helpers/open-create-social-post-modal';
import { saveSocialPost } from 'tests/main/posts/social/helpers/save-social-post';
import {
    selectPlatformInSocialPostModal,
    unselectPlatformInSocialPostModal,
} from 'tests/main/posts/social/helpers/social-platform-selection';

import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { ONE_MINUTE } from ':constants';
import { appendRandomCharacterToTextAtRandomPosition, generateRandomSentence } from ':helpers';

it.describe.configure({ retries: 0 });
const threeMinutesTimeout = 3 * ONE_MINUTE;
it.setTimeout(threeMinutesTimeout); // Publishing a post can take some time

it('should publish carousel post to Instagram', async ({ page }) => {
    // Go to social posts page
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/social/socialposts`);

    await openCreateSocialPostModal(page);
    const modal = page.locator('app-upsert-social-post-modal');

    await addMediaToSocialPost({ page, folderName: 'social', mediaCount: 3 });

    const POST_TEXT_CONTENT = appendRandomCharacterToTextAtRandomPosition(generateRandomSentence());
    await editSocialPostCaption({ locator: modal, newCaption: POST_TEXT_CONTENT });

    await selectPlatformInSocialPostModal(page, PlatformKey.INSTAGRAM);
    await unselectPlatformInSocialPostModal(page, PlatformKey.FACEBOOK);
    await unselectPlatformInSocialPostModal(page, PlatformKey.MAPSTR);
    // TODO uncomment this line when TikTok is activated for Le Nina
    // await unselectPlatformInSocialPostModal(page, PlatformKey.TIKTOK);

    await saveSocialPost({ page, status: 'now' });

    // Wait for post to be created and modal to be automatically closed
    await page.waitForResponse(/posts\/v2/);
    await expect(modal).not.toBeVisible();

    // Find post in the list and wait for it to be published
    const post = page.locator('app-social-post-item', { hasText: POST_TEXT_CONTENT }).first();
    await expect(post.getByTestId(`social-post-status-${PostPublicationStatus.PUBLISHED}`)).toBeVisible({ timeout: threeMinutesTimeout });
});
