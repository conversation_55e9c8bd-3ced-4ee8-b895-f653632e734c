import { expect, it } from 'baseTest';

import { ONE_MINUTE } from ':constants';

it.describe.configure({ retries: 0 });
const threeMinutesTimeout = 3 * ONE_MINUTE;
it.setTimeout(threeMinutesTimeout); // Syncing reviews can take some time

it('should sync reviews', async ({ page }) => {
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/reputation/reviews`);
    await page.getByTestId('reviews-sync-btn').click();

    await page.waitForResponse(
        async (response) => {
            if (response.url().includes('/currentState')) {
                const bodyText = await response.text();
                const body = JSON.parse(bodyText);

                if (body.data?.currentState?.reviews?.fetched) {
                    const fetchedState = body.data?.currentState?.reviews?.fetched as Record<string, { status: string; error?: string }>;
                    const fetchedPlatforms = Object.keys(fetchedState).map((platformKey) => ({
                        ...fetchedState[platformKey],
                        platformKey,
                    }));
                    if (fetchedPlatforms.length > 0) {
                        return fetchedPlatforms.every((f) => {
                            return f.status === 'success' || f.status === 'error'; // Some platforms are fetched even though they are not displayed to the user
                            // The check will be done in the next step, in the UI, with the green check icons
                        });
                    }
                }
            }
            return false;
        },
        { timeout: threeMinutesTimeout }
    );

    const footer = page.getByTestId('review-sync-footer-container');

    const matIcons = await footer.locator('mat-icon').all();

    for (const matIcon of matIcons) {
        await expect(matIcon).toHaveAttribute('data-mat-icon-name', 'check');
    }
});
