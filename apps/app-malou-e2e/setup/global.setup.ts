import { chromium, type FullConfig } from '@playwright/test';
import mongoose from 'mongoose';
import setIdsInEnv from 'setup/set-ids-in-env';
import validateDbPrecondition from 'setup/validate-db-precondition';
import setupKeywords from 'tests/main/keywords/keywords.setup';
import setupPlatforms from 'tests/main/platforms/platforms.setup';
import setupReports from 'tests/main/reports/reports.setup';
import setupReviews from 'tests/main/reviews/reviews.setup';
import setupAutomation from 'tests/main/settings/automation/automation.setup';

import { E2E_ADMIN_USER } from ':constants';
import { login } from ':shared/login';

async function globalSetup(config: FullConfig) {
    try {
        await validateDbPrecondition();
        await setIdsInEnv();
        await seeds();
        await disconnectDb();
        await loginUser(config);
    } catch (err) {
        console.error(err);
        process.exit(1);
    }
}

export async function seeds() {
    await setupPlatforms();
    await setupReports();
    await setupKeywords();
    await setupReviews();
    await setupAutomation();
}

async function disconnectDb() {
    await mongoose.connection.close();
}

async function loginUser(config: FullConfig) {
    const { baseURL, storageState } = config.projects[0]!.use;
    const browser = await chromium.launch();
    const page = await browser.newPage();
    await page.goto(baseURL!);
    await login({ page, email: E2E_ADMIN_USER.email, password: process.env.MALOU_E2E_PASSWORD! });
    await page.context().storageState({ path: storageState as string });
    await browser.close();
}

export default globalSetup;
