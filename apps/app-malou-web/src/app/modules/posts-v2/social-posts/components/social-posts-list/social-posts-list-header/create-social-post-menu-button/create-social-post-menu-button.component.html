<app-menu-button-v2
    testId="social-post-open-create-post-modal-btn"
    [disabled]="!(CaslAction.MANAGE | caslAble: CaslSubject.SOCIAL_POST)"
    [matTooltipDisabled]="CaslAction.MANAGE | caslAble: CaslSubject.SOCIAL_POST"
    [matTooltip]="'casl.wrong_role' | translate"
    [text]="'social_posts.create_post' | translate"
    [size]="MenuButtonSize.LARGE">
    <button
        class="flex !px-5"
        id="tracking_social_post_menu_open_create_post_modal_btn_v2"
        data-testid="social_post_menu_open_create_post_modal_btn_v2"
        mat-menu-item
        (click)="onCreatePost()">
        <mat-icon color="primary" [svgIcon]="SvgIcon.ADD"></mat-icon>
        <span class="malou-text-14--regular text-malou-color-text-2">{{ 'social_posts.publication' | translate }}</span>
    </button>
    <button
        class="flex !px-5"
        id="tracking_social_post_menu_open_create_reel_modal_btn_v2"
        data-testid="social_post_menu_open_create_reel_modal_btn_v2"
        mat-menu-item
        (click)="onCreateReelOrTikTok()">
        <mat-icon color="primary" [svgIcon]="SvgIcon.VIDEO"></mat-icon>
        <span class="malou-text-14--regular text-malou-color-text-2">
            {{ isTiktokEnabled() ? ('social_posts.reel_or_tiktok' | translate) : ('social_posts.Reel' | translate) }}
        </span>
    </button>
</app-menu-button-v2>
