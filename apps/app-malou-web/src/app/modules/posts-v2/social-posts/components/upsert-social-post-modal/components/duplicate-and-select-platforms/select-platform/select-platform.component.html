<div class="flex items-center justify-between" [matTooltip]="tooltip()">
    <div class="flex items-center gap-x-2" [ngClass]="{ 'opacity-50': disabled() }">
        <app-platform-logo class="h-7 w-7" [logo]="platformKey()"></app-platform-logo>

        <div class="malou-text-12--regular text-malou-color-text-2">{{ platformKey() | enumTranslate: 'platform_key' }}</div>
    </div>

    <app-slide-toggle
        [id]="trackingId()"
        [checked]="checked()"
        [disabled]="disabled()"
        [testId]="testId()"
        (onToggle)="onToggle($event)"></app-slide-toggle>
</div>
