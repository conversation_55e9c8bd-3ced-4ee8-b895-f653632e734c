import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, input, output, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

import { MediaType } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { VideoThumbnailSliderComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/components/reel-media/video-thumbnail-slider/video-thumbnail-slider.component';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { pickImagesFromVideo } from ':shared/helpers/video-cover-url';

export type Thumbnail =
    | { type: 'custom'; media: EditionMedia }
    | {
          type: 'videoFrame';
          thumbnailOffsetTimeInMs: number;
          /** if the media has been generated server-side */
          media?: EditionMedia;
      };

@Component({
    selector: 'app-reel-media',
    templateUrl: './reel-media.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [NgClass, VideoThumbnailSliderComponent, TranslateModule, MalouSpinnerComponent],
})
export class ReelMediaComponent {
    /** Must be a video (with type set to MediaType.PHOTO) */
    public readonly media = input.required<(EditionMedia & { type: MediaType.VIDEO }) | undefined>();

    public readonly thumbnail = input.required<Thumbnail | undefined>();

    public readonly isLoading = input.required<boolean>();
    readonly isReadonly = input<boolean>(false);
    readonly canSelectThumbnailFromMedia = input<boolean>(true);

    public readonly openThumbnailFilePicker = output();
    public readonly importThumbnailFromGallery = output();
    public readonly deleteMedia = output();

    /**
     * This output signal emits an object after the user has moved the slider
     */
    public readonly thumbnailSelectedFromVideo = output<{
        /** The time of the frame to capture as a floating point number between 0 (begin) and 1 (end) */
        timeFloat: number;

        /** Same value but in milliseconds */
        thumbnailOffsetTimeInMs: number;
    }>();

    readonly timelinePreviewUrls = signal<string[]>([]);

    /** between 0 and 1 */
    readonly timelineLoadingProgress = signal<number | undefined>(0);

    readonly sliderThumbnailUrl = computed((): string | undefined => {
        const inputThumbnail = this.thumbnail();
        if (inputThumbnail?.media?.thumbnail1024OutsideUrl) {
            return inputThumbnail?.media?.thumbnail1024OutsideUrl;
        }
    });

    readonly thumbnailCursorPosition = computed((): number | undefined => {
        const thumbnail = this.thumbnail();
        const videoDurationSeconds = this._videoDurationSeconds();
        if (!thumbnail) {
            return 0;
        }
        if (thumbnail.type === 'custom' || !videoDurationSeconds) {
            return undefined;
        }
        return thumbnail.thumbnailOffsetTimeInMs / 1000 / videoDurationSeconds;
    });

    private _videoUrl = computed(() => {
        const media = this.media();
        return media?.videoUrl;
    });

    private _videoDurationSeconds = signal<number | null>(null);

    constructor() {
        effect(() => {
            const media = this.media();
            if (media?.duration) {
                this._videoDurationSeconds.set(media.duration);
            }
        });

        // Generate the pictures of the timeline of the slider the after the component is created
        effect((onCleanup) => {
            let destroyed = false;

            const videoUrl = this._videoUrl();
            if (!videoUrl) {
                return;
            }

            this.timelineLoadingProgress.set(0);

            pickImagesFromVideo({
                videoUrl,
                resolutionPx: 200,
                numberOfImages: 5,
                onProgress: (progress) => {
                    this.timelineLoadingProgress.set(progress);
                },
            }).then(({ durationSeconds, urls }) => {
                if (destroyed) {
                    return;
                }
                this._videoDurationSeconds.set(durationSeconds);
                this.timelineLoadingProgress.set(undefined);
                this.timelinePreviewUrls.set(urls);
            });

            onCleanup(() => {
                destroyed = true;
                for (const url of this.timelinePreviewUrls()) {
                    window.URL.revokeObjectURL(url);
                }
                this.timelinePreviewUrls.set([]);
            });
        });
    }

    onPositionChange(position: number): void {
        const videoDurationSeconds = this._videoDurationSeconds();
        if (!videoDurationSeconds) {
            return;
        }
        this.thumbnailSelectedFromVideo.emit({
            timeFloat: position,
            thumbnailOffsetTimeInMs: position * videoDurationSeconds * 1000,
        });
    }
}
