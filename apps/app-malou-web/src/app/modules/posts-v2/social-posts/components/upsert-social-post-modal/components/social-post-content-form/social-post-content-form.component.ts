import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { ReelThumbnail } from '@malou-io/package-dto';
import { getPublicationType, PlatformKey, PostType, TiktokPrivacyStatus } from '@malou-io/package-utils';

import { SocialPostCaptionComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-caption/social-post-caption.component';
import { SocialPostCtaComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-cta/social-post-cta.component';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { SocialPostPlaceComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-place/social-post-place.component';
import { TagUsersInputComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/tag-users-input/tag-users-input.component';
import { TiktokOptionsComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/tiktok-options/tiktok-options.component';
import { UpsertSocialPostContext } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/contexts/upsert-social-post.context';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { FbLocation } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

import { instagramCollaboratorsInputComponent } from './instagram-collaborators-input/instagram-collaborators-input.component';
import { SocialPostMediasComponent } from './social-post-medias/social-post-medias.component';

@Component({
    selector: 'app-social-post-content-form',
    templateUrl: './social-post-content-form.component.html',
    styleUrls: ['./social-post-content-form.component.scss'],
    imports: [
        MatIconModule,
        MatTooltipModule,
        TranslateModule,
        SocialPostCaptionComponent,
        SocialPostCtaComponent,
        SocialPostPlaceComponent,
        InputTextComponent,
        SocialPostMediasComponent,
        TagUsersInputComponent,
        TiktokOptionsComponent,
        instagramCollaboratorsInputComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialPostContentFormComponent {
    readonly isReadonly = input.required<boolean>();

    private readonly _upsertSocialPostContext = inject(UpsertSocialPostContext);

    readonly postTitle = this._upsertSocialPostContext.upsertSocialPostState.post.title;
    readonly postCaption = this._upsertSocialPostContext.upsertSocialPostState.post.text;
    readonly postLocation = this._upsertSocialPostContext.upsertSocialPostState.post.location;
    readonly attachments = this._upsertSocialPostContext.upsertSocialPostState.post.attachments;
    readonly reelThumbnail = this._upsertSocialPostContext.upsertSocialPostState.post.reelThumbnail;
    readonly platformKeys = this._upsertSocialPostContext.upsertSocialPostState.post.platformKeys;

    readonly publicationType = computed(() =>
        getPublicationType(this._upsertSocialPostContext.upsertSocialPostState.post.postType(), false)
    );

    readonly isMapstrSelected = computed(() =>
        this._upsertSocialPostContext.upsertSocialPostState.post.platformKeys().includes(PlatformKey.MAPSTR)
    );
    readonly areFacebookOrInstagramSelected = computed(() =>
        this._upsertSocialPostContext.upsertSocialPostState.post
            .platformKeys()
            .some((platformKey) => [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM].includes(platformKey))
    );
    readonly isTiktokSelected = computed(() =>
        this._upsertSocialPostContext.upsertSocialPostState.post.platformKeys().includes(PlatformKey.TIKTOK)
    );
    readonly isInstagramSelected = computed(() =>
        this._upsertSocialPostContext.upsertSocialPostState.post.platformKeys().includes(PlatformKey.INSTAGRAM)
    );

    readonly isReel = computed(() => this._upsertSocialPostContext.upsertSocialPostState.post.postType() === PostType.REEL);

    readonly facebookPlatformSocialId = computed((): string | null => {
        const connectedPlatforms = this._upsertSocialPostContext.upsertSocialPostState.connectedSocialPlatforms();
        const facebookPlatform = connectedPlatforms.find((platform) => platform.key === PlatformKey.FACEBOOK);
        return facebookPlatform?.socialId ?? null;
    });

    readonly isLoadingLocation = this._upsertSocialPostContext.upsertSocialPostState.isLoadingLocation;

    readonly tiktokOptions = this._upsertSocialPostContext.upsertSocialPostState.post.tiktokOptions;
    readonly isTiktokCommentDisabled = this._upsertSocialPostContext.upsertSocialPostState.tiktokCreatorInfo.isCommentDisabled;
    readonly isTiktokDuetDisabled = this._upsertSocialPostContext.upsertSocialPostState.tiktokCreatorInfo.isDuetDisabled;
    readonly isTiktokStitchDisabled = this._upsertSocialPostContext.upsertSocialPostState.tiktokCreatorInfo.isStitchDisabled;

    readonly MAPSTR_TITLE_MAX_LENGTH = 40;

    readonly SvgIcon = SvgIcon;

    readonly isOnlyTiktokSelected = computed(() => {
        const platformKeys = this.platformKeys();
        return platformKeys.length === 1 && platformKeys[0] === PlatformKey.TIKTOK;
    });

    onTitleChange(title: string): void {
        this._upsertSocialPostContext.updateTitle(title);
    }

    onCaptionChange(caption: string): void {
        this._upsertSocialPostContext.updateCaption(caption);
    }

    onLocationChange(location: FbLocation | null): void {
        this._upsertSocialPostContext.updateLocation(location);
    }

    onMediaChange(medias: EditionMedia[]): void {
        this._upsertSocialPostContext.updateMedias(medias);
    }

    onPrivacyStatusChange(privacyStatus: TiktokPrivacyStatus): void {
        this._upsertSocialPostContext.updateTiktokPrivacyStatus(privacyStatus);
    }

    onContentDisclosureSettingsBrandedContentChange(brandedContent: boolean): void {
        this._upsertSocialPostContext.updateTiktokOptionsBrandedContent(brandedContent);
    }

    onContentDisclosureSettingsYourBrandChange(yourBrand: boolean): void {
        this._upsertSocialPostContext.updateTiktokOptionsYourBrand(yourBrand);
    }

    onContentDisclosureSettingsIsActivatedChange(isActivated: boolean): void {
        this._upsertSocialPostContext.updateTiktokOptionsIsActivated(isActivated);
    }

    onCommentChange(comment: boolean): void {
        this._upsertSocialPostContext.updateTiktokOptionsComment(comment);
    }

    onDuetChange(duet: boolean): void {
        this._upsertSocialPostContext.updateTiktokOptionsDuet(duet);
    }

    onStitchChange(stitch: boolean): void {
        this._upsertSocialPostContext.updateTiktokOptionsStitch(stitch);
    }

    onReelThumbnailChange(thumbnail: ReelThumbnail): void {
        this._upsertSocialPostContext.updateReelThumbnail(thumbnail);
    }
}
