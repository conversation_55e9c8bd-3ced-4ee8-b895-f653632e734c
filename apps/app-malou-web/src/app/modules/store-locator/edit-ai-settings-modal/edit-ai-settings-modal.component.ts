import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { partition } from 'lodash';

import { StoreLocatorAiSettingsDefaultTone, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { ScreenSizeService } from ':core/services/screen-size.service';
import { AiPreviewComponent } from ':modules/store-locator/edit-ai-settings-modal/ai-preview/ai-preview.component';
import {
    EditAiSettingsModalInputData,
    SpecialAttributeForm,
    StoreLocatorOrganizationKeyword,
} from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { KeywordsFormTabComponent } from ':modules/store-locator/edit-ai-settings-modal/keywords-form-tab/keywords-form-tab.component';
import { SpecialAttributesFormTabComponent } from ':modules/store-locator/edit-ai-settings-modal/special-attributes-form-tab/special-attributes-form-tab.component';
import { ToneFormTabComponent } from ':modules/store-locator/edit-ai-settings-modal/tone-form-tab/tone-form-tab.component';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { CloseWithoutSavingModalComponent } from ':shared/components/close-without-saving-modal/close-without-saving-modal.component';
import { StoreLocatorOrganizationRestaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-edit-ai-settings-modal',
    imports: [
        NgClass,
        NgTemplateOutlet,
        CloseWithoutSavingModalComponent,
        FormsModule,
        MatButtonModule,
        MatIconModule,
        MatTabsModule,
        TranslateModule,
        AiPreviewComponent,
        ToneFormTabComponent,
        KeywordsFormTabComponent,
        SpecialAttributesFormTabComponent,
        ReactiveFormsModule,
    ],
    templateUrl: './edit-ai-settings-modal.component.html',
    styleUrl: './edit-ai-settings-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditAiSettingsModalComponent implements OnInit {
    private readonly _dialogRef = inject(MatDialogRef<EditAiSettingsModalComponent>);
    private readonly _screenSizeService = inject(ScreenSizeService);
    private readonly _storeLocatorService = inject(StoreLocatorService);
    readonly data: EditAiSettingsModalInputData = inject(MAT_DIALOG_DATA);

    readonly StoreLocatorAiSettingsLanguageStyle = StoreLocatorAiSettingsLanguageStyle;
    readonly SvgIcon = SvgIcon;

    readonly displayCloseModal = signal(false);
    readonly isPhoneScreen = toSignal(this._screenSizeService.isPhoneScreen$, { initialValue: this._screenSizeService.isPhoneScreen });

    readonly defaultTones = Object.values(StoreLocatorAiSettingsDefaultTone);
    readonly selectedIndex = signal(0);

    readonly aiSettingsForm = new FormGroup({
        predefinedTones: new FormControl<string[]>([], { nonNullable: true }),
        customTones: new FormControl<string[]>([], { nonNullable: true }),
        languageStyle: new FormControl<StoreLocatorAiSettingsLanguageStyle>(StoreLocatorAiSettingsLanguageStyle.FORMAL, [
            Validators.required,
        ]),
        restaurantAttributeIds: new FormControl<string[]>([], { nonNullable: true }),
        restaurantKeywordIds: new FormControl<string[]>([], { nonNullable: true }),
        specialAttributes: new FormArray<FormGroup<SpecialAttributeForm>>([]),
    });

    readonly organizationRestaurants: WritableSignal<StoreLocatorOrganizationRestaurant[]> = signal([]);
    readonly organizationRestaurantKeywords: WritableSignal<StoreLocatorOrganizationKeyword[]> = signal([]);

    private _initialFormValue: any = null;

    ngOnInit(): void {
        console.log('Initializing EditAiSettingsModalComponent with data :>>', this.data);
        this.organizationRestaurants.set(this.data.organizationRestaurants);
        this.organizationRestaurantKeywords.set(this.data.organizationRestaurantKeywords);
        this._initializeForm();
        this._captureInitialFormValue();
    }

    submit(): void {
        const formValue = this.aiSettingsForm.getRawValue();
        const aiSettingsUpdate = {
            tone: [...formValue.predefinedTones, ...formValue.customTones],
            languageStyle: formValue.languageStyle ?? StoreLocatorAiSettingsLanguageStyle.FORMAL,
            restaurantAttributeIds: formValue.restaurantAttributeIds,
            restaurantKeywordIds: formValue.restaurantKeywordIds,
            specialAttributes: formValue.specialAttributes
                .map((attr) => ({
                    restaurantId: attr.restaurantId,
                    text: attr.text,
                }))
                .filter((attr) => !!attr?.text?.trim()),
        };

        this._storeLocatorService
            .updateOrganizationConfigurationAiSettings(this.data.organizationId, { aiSettings: aiSettingsUpdate })
            .subscribe({
                next: (updatedConfig) => {
                    this._dialogRef.close(updatedConfig);
                },
                error: (error) => {
                    console.error('Error updating AI settings:', error);
                },
            });
    }

    confirmClose(): void {
        this._dialogRef.close();
    }

    onTabChanged(event: MatTabChangeEvent): void {
        this.selectedIndex.set(event.index);
    }

    close(): void {
        if (this._hasFormChanged()) {
            this.displayCloseModal.set(true);
        } else {
            this.confirmClose();
        }
    }

    private _initializeForm(): void {
        const { tone, languageStyle, restaurantAttributeIds, restaurantKeywordIds, specialAttributes } = this.data.aiSettings;

        const [predefinedTones, customTones] = partition(tone || [], (t) =>
            Object.values(StoreLocatorAiSettingsDefaultTone).includes(t as StoreLocatorAiSettingsDefaultTone)
        );

        // Patch the basic form values
        this.aiSettingsForm.patchValue({
            predefinedTones: predefinedTones || [],
            customTones: customTones || [],
            languageStyle: languageStyle || StoreLocatorAiSettingsLanguageStyle.FORMAL,
            restaurantAttributeIds: restaurantAttributeIds || [],
            restaurantKeywordIds: restaurantKeywordIds || [],
        });

        // Handle specialAttributes FormArray separately
        const specialAttributesArray = this.aiSettingsForm.controls.specialAttributes;
        specialAttributesArray.clear();

        if (specialAttributes?.length > 0) {
            specialAttributes.forEach((attr) => {
                const formGroup = new FormGroup<SpecialAttributeForm>({
                    restaurantId: new FormControl(attr.restaurantId, {
                        nonNullable: true,
                        validators: [Validators.required, Validators.minLength(1)],
                    }),
                    text: new FormControl(attr.text, { nonNullable: true, validators: [Validators.required, Validators.minLength(1)] }),
                });
                specialAttributesArray.push(formGroup);
            });
        } else {
            const emptyFormGroup = new FormGroup<SpecialAttributeForm>({
                restaurantId: new FormControl('', { nonNullable: true, validators: [Validators.required, Validators.minLength(1)] }),
                text: new FormControl('', { nonNullable: true, validators: [Validators.required, Validators.minLength(1)] }),
            });
            specialAttributesArray.push(emptyFormGroup);
        }

        // Store the initial form value for change detection
        this._initialFormValue = this.aiSettingsForm.getRawValue();
    }

    private _captureInitialFormValue(): void {
        this._initialFormValue = this._getFormSnapshot();
    }

    private _hasFormChanged(): boolean {
        const currentFormValue = this._getFormSnapshot();
        return !this._areFormValuesEqual(this._initialFormValue, currentFormValue);
    }

    private _getFormSnapshot(): any {
        return {
            predefinedTones: [...(this.aiSettingsForm.value.predefinedTones || [])],
            customTones: [...(this.aiSettingsForm.value.customTones || [])],
            languageStyle: this.aiSettingsForm.value.languageStyle,
            restaurantAttributeIds: [...(this.aiSettingsForm.value.restaurantAttributeIds || [])],
            restaurantKeywordIds: [...(this.aiSettingsForm.value.restaurantKeywordIds || [])],
            specialAttributes:
                this.aiSettingsForm.value.specialAttributes?.map((attr) => ({
                    restaurantId: attr?.restaurantId,
                    text: attr?.text,
                })) || [],
        };
    }

    private _areFormValuesEqual(value1: any, value2: any): boolean {
        return JSON.stringify(value1) === JSON.stringify(value2);
    }
}
