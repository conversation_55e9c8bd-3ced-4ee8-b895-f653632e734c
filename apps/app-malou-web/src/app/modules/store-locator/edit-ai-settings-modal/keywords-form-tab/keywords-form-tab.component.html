<div class="flex w-full flex-col gap-6">
    <div class="flex flex-col gap-3">
        <div class="malou-text-14--bold text-malou-color-text-1">
            {{ 'store_locator.edit_ai_settings_modal.keywords_form.characteristics' | translate }}
        </div>
        <div class="grid grid-cols-3 gap-2">
            @for (category of allAttributes(); track category.restaurantAttributeId) {
                <div class="flex items-center gap-x-2">
                    <mat-checkbox
                        color="primary"
                        [checked]="isAttributChecked | applyPure: category.restaurantAttributeId"
                        (change)="onAttributeToggle(category.restaurantAttributeId, $event.checked)">
                        <span class="malou-text-12--medium text-malou-color-text-1">
                            {{ category.attributeName.fr }}
                        </span>
                    </mat-checkbox>
                </div>
            }
        </div>
    </div>

    <div class="flex flex-col gap-3">
        <div class="malou-text-14--bold text-malou-color-text-1">
            {{ 'store_locator.edit_ai_settings_modal.keywords_form.keywords' | translate }}
        </div>
        <div class="grid grid-cols-3 gap-2">
            @for (keyword of allKeywords(); track keyword.restaurantKeywordId) {
                <div class="flex items-center gap-x-2">
                    <mat-checkbox
                        color="primary"
                        [checked]="isKeywordChecked | applyPure: keyword.restaurantKeywordId"
                        (change)="onKeywordToggle(keyword.restaurantKeywordId, $event.checked)">
                        <span class="malou-text-12--medium text-malou-color-text-1">
                            {{ keyword.text }}
                        </span>
                    </mat-checkbox>
                </div>
            }
        </div>
    </div>
</div>
