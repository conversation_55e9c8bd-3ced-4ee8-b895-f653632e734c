import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TranslateModule } from '@ngx-translate/core';
import { uniqBy } from 'lodash';

import { StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import {
    SpecialAttributeForm,
    StoreLocatorOrganizationKeyword,
} from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { StoreLocatorOrganizationRestaurant } from ':shared/models';
import { StoreLocatorOrganizationConfiguration } from ':shared/models/store-locator-organization-config';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';

type AiSettingsAttribute = StoreLocatorOrganizationConfiguration['aiSettings']['attributes'][number];
type AiSettingsKeyword = StoreLocatorOrganizationConfiguration['aiSettings']['keywords'][number];

@Component({
    selector: 'app-keywords-form-tab',
    imports: [FormsModule, ReactiveFormsModule, MatCheckboxModule, ApplyPurePipe, TranslateModule],
    templateUrl: './keywords-form-tab.component.html',
    styleUrl: './keywords-form-tab.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class KeywordsFormTabComponent {
    readonly storeLocatorAiSettings = input.required<StoreLocatorOrganizationConfiguration['aiSettings']>();
    readonly aiSettingsForm = input.required<
        FormGroup<{
            predefinedTones: FormControl<string[]>;
            customTones: FormControl<string[]>;
            languageStyle: FormControl<StoreLocatorAiSettingsLanguageStyle | null>;
            restaurantAttributeIds: FormControl<string[]>;
            restaurantKeywordIds: FormControl<string[]>;
            specialAttributes: FormArray<FormGroup<SpecialAttributeForm>>;
        }>
    >();
    readonly organizationRestaurants = input.required<StoreLocatorOrganizationRestaurant[]>();
    readonly organizationRestaurantKeywords = input.required<StoreLocatorOrganizationKeyword[]>();

    readonly allKeywords = computed<AiSettingsKeyword[]>(() => {
        const organizationKeywords = this._getKeywordsToDisplay(this.organizationRestaurantKeywords());
        const allkeywords = [...organizationKeywords, ...this.storeLocatorAiSettings().keywords];
        return uniqBy(allkeywords, 'restaurantKeywordId');
    });

    readonly allAttributes = computed<AiSettingsAttribute[]>(() => {
        const restaurantsAttributes = this._getRestaurantsAttributesToDisplay(this.organizationRestaurants());
        const allAttributes = [...restaurantsAttributes, ...this.storeLocatorAiSettings().attributes];
        return uniqBy(allAttributes, 'restaurantAttributeId');
    });

    onAttributeToggle(restaurantAttributeId: string, isChecked: boolean): void {
        const ids = this.aiSettingsForm().controls.restaurantAttributeIds.value || [];
        const updatedIds = isChecked ? [...ids, restaurantAttributeId] : ids.filter((id) => id !== restaurantAttributeId);
        this.aiSettingsForm().controls.restaurantAttributeIds.setValue(updatedIds);
    }

    onKeywordToggle(restaurantKeywordId: string, isChecked: boolean): void {
        const ids = this.aiSettingsForm().controls.restaurantKeywordIds.value || [];
        const updatedIds = isChecked ? [...ids, restaurantKeywordId] : ids.filter((id) => id !== restaurantKeywordId);
        this.aiSettingsForm().controls.restaurantKeywordIds.setValue(updatedIds);
    }

    isAttributChecked = (restaurantAttributeId: string): boolean => {
        const restaurantAttributeIds = this.aiSettingsForm().controls.restaurantAttributeIds.value || [];
        return restaurantAttributeIds.includes(restaurantAttributeId);
    };

    isKeywordChecked = (restaurantKeywordId: string): boolean => {
        const restaurantKeywordIds = this.aiSettingsForm().controls.restaurantKeywordIds.value || [];
        return restaurantKeywordIds.includes(restaurantKeywordId);
    };

    private _getRestaurantsAttributesToDisplay(organizationRestaurants: StoreLocatorOrganizationRestaurant[]): AiSettingsAttribute[] {
        return organizationRestaurants.flatMap((restaurant) => restaurant.attributeList ?? []);
    }

    private _getKeywordsToDisplay(organizationRestaurantKeywords: StoreLocatorOrganizationKeyword[]): AiSettingsKeyword[] {
        return organizationRestaurantKeywords;
    }
}
