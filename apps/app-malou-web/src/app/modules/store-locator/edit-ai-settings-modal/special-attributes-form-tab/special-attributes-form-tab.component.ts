import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { SpecialAttributeForm } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { StoreLocatorOrganizationRestaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-special-attributes-form-tab',
    imports: [
        MatButtonModule,
        MatIconModule,
        FormsModule,
        ReactiveFormsModule,
        SelectComponent,
        InputTextComponent,
        TranslateModule,
        NgClass,
    ],
    templateUrl: './special-attributes-form-tab.component.html',
    styleUrl: './special-attributes-form-tab.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SpecialAttributesFormTabComponent {
    readonly organizationRestaurants = input.required<StoreLocatorOrganizationRestaurant[]>();
    readonly aiSettingsForm = input.required<
        FormGroup<{
            predefinedTones: FormControl<string[]>;
            customTones: FormControl<string[]>;
            languageStyle: FormControl<StoreLocatorAiSettingsLanguageStyle | null>;
            restaurantAttributeIds: FormControl<string[]>;
            restaurantKeywordIds: FormControl<string[]>;
            specialAttributes: FormArray<FormGroup<SpecialAttributeForm>>;
        }>
    >();

    readonly restaurantIds = computed(() => this.organizationRestaurants().map((restaurant) => restaurant.id));

    readonly SvgIcon = SvgIcon;

    get specialAttributes(): FormArray<FormGroup<SpecialAttributeForm>> {
        return this.aiSettingsForm().controls.specialAttributes;
    }

    addSpecialAttribute(): void {
        this.specialAttributes.push(
            new FormGroup<SpecialAttributeForm>({
                restaurantId: new FormControl<string>('', {
                    nonNullable: true,
                    validators: [Validators.required, Validators.minLength(1)],
                }),
                text: new FormControl<string>('', { nonNullable: true, validators: [Validators.required, Validators.minLength(1)] }),
            })
        );
    }

    removeSpecialAttribute(index: number): void {
        this.specialAttributes.removeAt(index);
    }

    restaurantDisplayWith = (restaurantId: string): string => {
        const restaurant = this.organizationRestaurants().find((r) => r.id === restaurantId);
        return restaurant?.internalName || restaurant?.name || restaurantId;
    };
}
