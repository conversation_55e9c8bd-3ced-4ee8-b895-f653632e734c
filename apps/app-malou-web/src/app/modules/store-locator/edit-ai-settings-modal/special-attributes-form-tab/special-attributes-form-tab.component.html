<div class="flex w-full flex-col gap-2">
    <form class="flex flex-col gap-4" [formGroup]="aiSettingsForm()">
        <div class="malou-text-14--regular mb-4 italic text-malou-color-text-2">
            {{ 'store_locator.edit_ai_settings_modal.special_attributes_form.description' | translate }}
        </div>
        @for (specialAttribute of specialAttributes.controls; track $index; let index = $index; let last = $last) {
            <div class="flex flex-col gap-2">
                <div class="malou-text-14--bold text-malou-color-text-1">
                    {{ 'store_locator.edit_ai_settings_modal.special_attributes_form.particularity' | translate }} {{ index + 1 }}
                </div>
                <div class="flex w-full items-center justify-around gap-3">
                    <div
                        class="flex-1"
                        [ngClass]="{
                            'mb-4':
                                specialAttributes.controls[index].controls.restaurantId.valid &&
                                !specialAttributes.controls[index].controls.text.valid,
                        }">
                        <app-select
                            [title]="'store_locator.edit_ai_settings_modal.special_attributes_form.establishment' | translate"
                            [formControl]="specialAttribute.controls['restaurantId']"
                            [values]="restaurantIds()"
                            [displayWith]="restaurantDisplayWith"
                            [errorMessage]="
                                !specialAttributes.controls[index].controls.restaurantId.valid ? ('common.field_required' | translate) : ''
                            "
                            [placeholder]="
                                'store_locator.edit_ai_settings_modal.special_attributes_form.establishment_placeholder' | translate
                            ">
                        </app-select>
                    </div>
                    <div
                        class="flex-2"
                        [ngClass]="{
                            'mb-4':
                                !specialAttributes.controls[index].controls.restaurantId.valid &&
                                specialAttributes.controls[index].controls.text.valid,
                        }">
                        <app-input-text
                            [title]="'store_locator.edit_ai_settings_modal.special_attributes_form.particularities' | translate"
                            [formControl]="specialAttribute.controls['text']"
                            [errorMessage]="
                                !specialAttributes.controls[index].controls.text.valid ? ('common.field_required' | translate) : ''
                            "
                            [placeholder]="
                                'store_locator.edit_ai_settings_modal.special_attributes_form.particularities_placeholder' | translate
                            ">
                        </app-input-text>
                    </div>

                    <div class="mt-5 flex items-center">
                        <mat-icon
                            class="malou-color-chart-pink--accent !h-5 !w-5 cursor-pointer"
                            svgIcon="trash"
                            [ngClass]="{ 'mb-4': !specialAttributes.controls[index].controls.text.valid }"
                            (click)="removeSpecialAttribute(index)">
                        </mat-icon>
                    </div>
                </div>

                @if (last) {
                    <div class="flex">
                        <button class="malou-btn-flat" mat-button (click)="addSpecialAttribute()">
                            <mat-icon svgIcon="add"></mat-icon>
                            {{ 'store_locator.edit_ai_settings_modal.special_attributes_form.add_particularity' | translate }}
                        </button>
                    </div>
                }
            </div>
        }
    </form>
</div>
