import { FormControl } from '@angular/forms';

import { StoreLocatorOrganizationKeywordDto } from '@malou-io/package-dto';

import { StoreLocatorOrganizationRestaurant } from ':shared/models';
import { StoreLocatorOrganizationConfiguration } from ':shared/models/store-locator-organization-config';

export type StoreLocatorOrganizationKeyword = StoreLocatorOrganizationKeywordDto;

export interface SpecialAttributeForm {
    restaurantId: FormControl<string>;
    text: FormControl<string>;
}

export interface EditAiSettingsModalInputData {
    organizationId: string;
    aiSettings: StoreLocatorOrganizationConfiguration['aiSettings'];
    organizationRestaurants: StoreLocatorOrganizationRestaurant[];
    organizationRestaurantKeywords: StoreLocatorOrganizationKeyword[];
}
