<div class="h-full bg-malou-color-background-light">
    <div class="malou-dialog__header !p-4 !pl-5">
        <div class="title">
            <div class="malou-text-18--bold text-malou-color-text-1">
                {{ 'store_locator.edit_ai_settings_modal.preview.title' | translate }}
            </div>
        </div>
        <button class="malou-btn-icon" mat-icon-button (click)="close()">
            <mat-icon color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
        </button>
    </div>

    <div class="malou-dialog__body h-full max-h-[70vh] overflow-y-auto !pl-5 pb-3">
        <div class="flex w-full">
            <button
                class="malou-btn-raised--secondary--alt !h-10 grow !text-malou-color-chart-purple--accent"
                mat-raised-button
                (click)="generatePreview()">
                <div class="flex items-center justify-center">
                    <mat-icon class="mb-1 mr-1 h-4 !w-4 text-malou-color-chart-purple--accent" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                    <span class="text-malou-color-chart-purple--accent">
                        @if (!generationPreview()) {
                            <span>{{ 'store_locator.edit_ai_settings_modal.preview.generate_button' | translate }}</span>
                        } @else {
                            <span>{{ 'store_locator.edit_ai_settings_modal.preview.retry_button' | translate }}</span>
                        }
                    </span>
                </div>
            </button>
        </div>

        @if (generationPreview()) {
            <div class="mb-4 mt-2 rounded-lg bg-malou-color-background-dark--light p-4">
                <span class="malou-text-12--regular text-malou-color-text-2">{{ generationPreview() }}</span>
            </div>
        }
    </div>
</div>
