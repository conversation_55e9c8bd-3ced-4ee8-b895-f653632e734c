import { ChangeDetectionStrategy, Component, input, output, signal } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { SpecialAttributeForm } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-ai-preview',
    imports: [MatButtonModule, MatIconModule, TranslateModule],
    templateUrl: './ai-preview.component.html',
    styleUrl: './ai-preview.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AiPreviewComponent {
    readonly emitClose = output<void>();
    readonly aiSettingsForm = input.required<
        FormGroup<{
            predefinedTones: FormControl<string[]>;
            customTones: FormControl<string[]>;
            languageStyle: FormControl<StoreLocatorAiSettingsLanguageStyle | null>;
            restaurantAttributeIds: FormControl<string[]>;
            restaurantKeywordIds: FormControl<string[]>;
            specialAttributes: FormArray<FormGroup<SpecialAttributeForm>>;
        }>
    >();

    readonly generationPreview = signal<string>('');

    readonly SvgIcon = SvgIcon;

    generatePreview(): void {
        // TODO: Implement the logic to generate a preview based on the AI settings form values.
        const _formValue = this.aiSettingsForm().value;
        const loremIpsum =
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.';
        this.generationPreview.set(loremIpsum);
    }

    close(): void {
        this.emitClose.emit();
    }
}
