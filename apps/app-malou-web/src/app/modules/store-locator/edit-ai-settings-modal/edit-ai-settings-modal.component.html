<div
    class="edit-modal-container"
    [ngClass]="{
        'h-[90vh]': !displayCloseModal() || (displayCloseModal() && isPhoneScreen()),
        'h-[350px]': displayCloseModal() && !isPhoneScreen(),
    }">
    <ng-container *ngTemplateOutlet="displayCloseModal() ? closeModal : editModal"></ng-container>
</div>

<ng-template #closeModal>
    <app-close-without-saving-modal (onConfirm)="confirmClose()" (onCancel)="displayCloseModal.set(false)">
    </app-close-without-saving-modal>
</ng-template>

<ng-template #editModal>
    <div class="malou-dialog h-full w-full !flex-row">
        <div class="flex w-[70%] flex-col justify-between">
            <ng-container *ngTemplateOutlet="settingsTemplate"></ng-container>
        </div>
        <div class="flex w-[30%] flex-col justify-between">
            <ng-container *ngTemplateOutlet="previewTemplate"></ng-container>
        </div>
    </div>
</ng-template>

<ng-template #settingsTemplate>
    <div class="malou-dialog__header !py-5">
        <div class="title">
            <div class="malou-text-18--bold text-malou-color-text-1">{{ 'store_locator.edit_ai_settings_modal.title' | translate }}</div>
        </div>
    </div>

    <div class="malou-dialog__body h-[75%] md:!overflow-y-scroll">
        <ng-container *ngTemplateOutlet="formTemplate"></ng-container>
    </div>

    <div class="malou-dialog__footer">
        <button class="malou-btn-raised--secondary--alt !h-11 md:grow" mat-raised-button (click)="close()">
            {{ 'common.cancel' | translate }}
        </button>
        <button class="malou-btn-raised--primary !h-11 md:grow" mat-raised-button [disabled]="!aiSettingsForm.valid" (click)="submit()">
            {{ 'common.save' | translate }}
        </button>
    </div>
</ng-template>

<ng-template #previewTemplate>
    <div class="h-full">
        <app-ai-preview [aiSettingsForm]="aiSettingsForm" (emitClose)="close()"></app-ai-preview>
    </div>
</ng-template>

<ng-template #formTemplate>
    <mat-tab-group class="h-full" animationDuration="200" mat-align-tabs="start" (selectedTabChange)="onTabChanged($event)">
        <mat-tab [label]="'store_locator.edit_ai_settings_modal.tabs.tone' | translate">
            <div class="my-4 flex max-h-[55vh] flex-col justify-between">
                <app-tone-form-tab [aiSettingsForm]="aiSettingsForm"></app-tone-form-tab>
            </div>
        </mat-tab>
        <mat-tab [label]="'store_locator.edit_ai_settings_modal.tabs.specificities' | translate">
            <div class="my-4 flex max-h-[55vh] flex-col justify-between">
                <app-keywords-form-tab
                    [storeLocatorAiSettings]="data.aiSettings"
                    [aiSettingsForm]="aiSettingsForm"
                    [organizationRestaurantKeywords]="organizationRestaurantKeywords()"
                    [organizationRestaurants]="organizationRestaurants()">
                </app-keywords-form-tab>
            </div>
        </mat-tab>
        <mat-tab [label]="'store_locator.edit_ai_settings_modal.tabs.special_attributes' | translate">
            <div class="my-4 flex max-h-[55vh] flex-col justify-between">
                <app-special-attributes-form-tab [aiSettingsForm]="aiSettingsForm" [organizationRestaurants]="organizationRestaurants()">
                </app-special-attributes-form-tab>
            </div>
        </mat-tab>
    </mat-tab-group>
</ng-template>
