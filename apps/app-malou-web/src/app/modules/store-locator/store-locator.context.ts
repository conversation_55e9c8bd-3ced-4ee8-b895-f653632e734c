import { DestroyRef, inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { forkJoin, of } from 'rxjs';
import { distinctUntilChanged, filter, switchMap, tap } from 'rxjs/operators';

import { StoreLocatorOrganizationKeywordDto } from '@malou-io/package-dto';
import { isNotNil } from '@malou-io/package-utils';

import { KeywordsService } from ':core/services/keywords.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { StoreLocatorOrganizationKeyword } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { Restaurant, StoreLocatorOrganizationRestaurant } from ':shared/models';
import { StoreLocatorOrganizationConfiguration } from ':shared/models/store-locator-organization-config';

@Injectable({
    providedIn: 'root',
})
export class StoreLocatorContext {
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _keywordsService = inject(KeywordsService);
    private readonly _destroyRef = inject(DestroyRef);

    readonly isLoading = signal<boolean>(false);
    readonly storeLocatorOrganizationConfiguration = signal<StoreLocatorOrganizationConfiguration>(
        StoreLocatorOrganizationConfiguration.factory()
    );
    readonly storeLocatorOrganizationRestaurants = signal<StoreLocatorOrganizationRestaurant[]>([]);
    readonly organizationRestaurantKeywords = signal<StoreLocatorOrganizationKeyword[]>([]);

    constructor() {
        this._restaurantsService.restaurantSelected$
            .pipe(
                filter(isNotNil),
                distinctUntilChanged((prev: Restaurant, curr: Restaurant) => prev?.organizationId === curr?.organizationId),
                filter((restaurant: Restaurant) => isNotNil(restaurant.organizationId)),
                tap(() => this.isLoading.set(true)),
                switchMap((restaurant: Restaurant) =>
                    forkJoin([
                        of(restaurant.organizationId!),
                        this._restaurantsService.getStoreLocatorOrganizationRestaurants(restaurant.organizationId!),
                    ])
                ),
                switchMap(([organizationId, organizationRestaurants]: [string, StoreLocatorOrganizationRestaurant[]]) => {
                    const restaurantIds = organizationRestaurants.map((restaurant) => restaurant.id);
                    return forkJoin([
                        this._storeLocatorService.getOrganizationConfiguration(organizationId),
                        of(organizationRestaurants),
                        this._keywordsService.getStoreLocatorOrganizationKeywords({ restaurantIds }),
                    ]);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: ([organizationConfiguration, organizationRestaurants, organizationRestaurantKeywords]: [
                    StoreLocatorOrganizationConfiguration,
                    StoreLocatorOrganizationRestaurant[],
                    StoreLocatorOrganizationKeywordDto[],
                ]) => {
                    this.storeLocatorOrganizationConfiguration.set(organizationConfiguration);
                    this.storeLocatorOrganizationRestaurants.set(organizationRestaurants);
                    this.organizationRestaurantKeywords.set(organizationRestaurantKeywords);
                    this.isLoading.set(false);
                },
                error: () => {
                    this.isLoading.set(false);
                },
            });
    }

    updateStoreLocatorOrganizationConfiguration(storeLocatorOrganizationConfiguration: StoreLocatorOrganizationConfiguration): void {
        this.storeLocatorOrganizationConfiguration.set(storeLocatorOrganizationConfiguration);
    }
}
