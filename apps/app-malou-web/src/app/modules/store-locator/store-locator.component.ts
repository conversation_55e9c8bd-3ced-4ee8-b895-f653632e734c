import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ToastService } from ':core/services/toast.service';
import { EditAiSettingsModalComponent } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.component';
import { EditAiSettingsModalInputData } from ':modules/store-locator/edit-ai-settings-modal/edit-ai-settings-modal.interface';
import { EditStoreLocatorPageModalComponent } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.component';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { StoreLocatorOrganizationConfiguration } from ':shared/models/store-locator-organization-config';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { CustomDialogService, DialogScreenSize } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-store-locator',
    templateUrl: './store-locator.component.html',
    styleUrls: ['./store-locator.component.scss'],
    imports: [MatButtonModule, NgTemplateOutlet, IllustrationPathResolverPipe, MatIconModule, MatProgressSpinnerModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorComponent implements OnInit {
    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _toastService = inject(ToastService);
    private readonly _translate = inject(TranslateService);

    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;

    readonly storeLocatorOrganizationConfiguration = computed(() => this._storeLocatorContext.storeLocatorOrganizationConfiguration());
    readonly isLoading = computed(() => this._storeLocatorContext.isLoading());

    ngOnInit(): void {}

    editStoreLocatorPage(): void {
        this._customDialogService
            .open<EditStoreLocatorPageModalComponent>(
                EditStoreLocatorPageModalComponent,
                {
                    width: '100%',
                    height: '100%',
                    panelClass: 'malou-dialog-panel--without-border-radius',
                    disableClose: true,
                },
                { animateScreenSize: DialogScreenSize.ALL }
            )
            .afterClosed()
            .subscribe();
    }

    openEditAiSettingsModal(): void {
        const storeLocatorOrganizationConfiguration: StoreLocatorOrganizationConfiguration | null =
            this._storeLocatorContext.storeLocatorOrganizationConfiguration();

        if (!storeLocatorOrganizationConfiguration) {
            return;
        }

        const data: EditAiSettingsModalInputData = {
            organizationId: storeLocatorOrganizationConfiguration.organizationId,
            aiSettings: storeLocatorOrganizationConfiguration.aiSettings,
            organizationRestaurants: this._storeLocatorContext.storeLocatorOrganizationRestaurants(),
            organizationRestaurantKeywords: this._storeLocatorContext.organizationRestaurantKeywords(),
        };

        this._customDialogService
            .open<EditAiSettingsModalComponent, EditAiSettingsModalInputData>(EditAiSettingsModalComponent, {
                height: 'unset',
                maxHeight: '90vh',
                width: '90vw',
                maxWidth: '80vw',
                data,
            })
            .afterClosed()
            .subscribe({
                next: (result: StoreLocatorOrganizationConfiguration | undefined) => {
                    if (result) {
                        this._storeLocatorContext.updateStoreLocatorOrganizationConfiguration(result);
                        this._toastService.openSuccessToast(
                            this._translate.instant('store_locator.edit_ai_settings_modal.success_message')
                        );
                    }
                },
                error: (error) => {
                    console.error('Error opening EditAiSettingsModal:', error);
                },
            });
    }
}
