import { FormControl, FormGroup } from '@angular/forms';

export interface InformationBlockContentForm {
    title: FormControl<string>;
    cta: FormGroup<{
        text: FormControl<string>;
        url: FormControl<string>;
    }>;
}

export interface InformationBlockContent {
    title: string;
    cta?: {
        text: string;
        url: string;
    };
}

export interface InformationBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
        textColor: string;
        iconColor: string;
    };
    buttons: {
        primaryBackgroundColor: string;
        primaryBorderColor: string;
        primaryTextColor: string;
    };
}
export interface InformationBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
        textColor: FormControl<string>;
        iconColor: FormControl<string>;
    }>;
    buttons: FormGroup<{
        primaryBackgroundColor: FormControl<string>;
        primaryBorderColor: FormControl<string>;
        primaryTextColor: FormControl<string>;
    }>;
}

export enum InformationBlockContentFormInputValidation {
    TITLE_MIN_LENGTH = 5,
    TITLE_MAX_LENGTH = 60,
    CTA_TEXT_MIN_LENGTH = 5,
    CTA_URL_MIN_LENGTH = 3,
}
