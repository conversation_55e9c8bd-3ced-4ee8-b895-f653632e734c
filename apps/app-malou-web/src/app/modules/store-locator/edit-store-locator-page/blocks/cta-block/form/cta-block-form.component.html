<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <app-input-text
            class="malou-text-14--bold!"
            formControlName="title"
            [defaultValue]="title()"
            [title]="'store_locator.edit.controls.title.name' | translate"
            [disabled]="false"
            [errorMessage]="
                titleControl?.errors && (titleControl?.errors?.minlength || titleControl?.errors?.maxlength)
                    ? ('store_locator.edit.controls.title.length_error' | translate)
                    : ''
            "
            [isEmojiPickerEnabled]="true"
            [placeholder]="'store_locator.edit.controls.title.placeholder' | translate"
            [autocapitalize]="'none'">
        </app-input-text>

        <div class="expansion-header malou-expansion-panel">
            <mat-accordion>
                <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                    <mat-expansion-panel-header class="!pl-0" (click)="isButtonsPanelExpanded.set(!isButtonsPanelExpanded())">
                        <div class="flex w-full items-center justify-between">
                            <div class="malou-text-13--bold text-malou-color-text-1">
                                {{ 'store_locator.edit.controls.cta.header' | translate }}
                            </div>
                            <div class="flex items-center">
                                <mat-icon
                                    class="!w-3 transition-all"
                                    color="primary"
                                    [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                    [class.rotate-180]="isButtonsPanelExpanded()"></mat-icon>
                            </div>
                        </div>
                    </mat-expansion-panel-header>

                    <ng-template matExpansionPanelContent>
                        <div class="flex flex-col gap-6 !bg-malou-color-background-light" formArrayName="ctaButtons">
                            @for (ctaButton of ctaButtons.controls; let index = $index; track index) {
                                <div class="group flex gap-2" [formGroupName]="index">
                                    <div class="flex w-[85%] flex-col gap-2">
                                        <app-input-text
                                            class="malou-text-14--bold!"
                                            formControlName="text"
                                            [defaultValue]="ctaButton.get('text')?.value"
                                            [disabled]="false"
                                            [placeholder]="'store_locator.edit.controls.cta.title' | translate"
                                            [autocapitalize]="'none'">
                                        </app-input-text>
                                        <app-input-text
                                            class="malou-text-14--bold!"
                                            formControlName="url"
                                            [defaultValue]="ctaButton.get('url')?.value"
                                            [disabled]="false"
                                            [placeholder]="'store_locator.edit.controls.cta.placeholder' | translate"
                                            [autocapitalize]="'none'">
                                        </app-input-text>
                                    </div>
                                    <div class="flex items-start gap-2 pt-3">
                                        @if (index > 0) {
                                            <mat-icon
                                                class="invisible !w-4 cursor-pointer !fill-malou-color-chart-pink--accent group-hover:visible"
                                                [svgIcon]="SvgIcon.MINUS_CIRCLE"
                                                (click)="removeCtaButton(index)"></mat-icon>
                                        }
                                        <mat-icon
                                            class="invisible !w-4 cursor-pointer group-hover:visible"
                                            color="primary"
                                            [svgIcon]="SvgIcon.ADD"
                                            (click)="addCtaButton(index)"></mat-icon>
                                    </div>
                                </div>
                            }
                        </div>
                    </ng-template>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />
        </div>
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="buttons">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit.style.buttons.title' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.buttons.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.buttons.border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.borderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.buttons.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.textColor')" />
        </div>
    </form>
</ng-template>
