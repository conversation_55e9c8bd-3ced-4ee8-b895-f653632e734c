<div class="flex h-full flex-col gap-y-4">
    <div class="flex h-full flex-col">
        <mat-tab-group
            class="h-full"
            animationDuration="200"
            mat-align-tabs="start"
            [disableRipple]="true"
            [selectedIndex]="selectedTabIndex()"
            (selectedIndexChange)="handleTabChange($event)">
            <mat-tab label="{{ 'store_locator.edit.tabs.content' | translate }}">
                <div class="flex h-full flex-col justify-between py-2 pr-3">
                    <div class="mb-5 mt-5 flex flex-col gap-5">
                        <div>
                            <app-store-locator-edit-page-organization-restaurants-selector
                                [currentEditingRestaurant]="currentEditingRestaurant()"
                                [organizationRestaurants]="organizationRestaurants()"
                                [control]="restaurantsFilterControl" />
                        </div>
                        @if (contentFormTemplate()) {
                            <ng-container [ngTemplateOutlet]="contentFormTemplate()!"></ng-container>
                        }
                    </div>

                    <button class="malou-btn-raised--primary" mat-raised-button>
                        <div class="flex w-full justify-center gap-2">
                            <mat-icon class="!h-4 !w-4 text-white" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                            {{ 'store_locator.edit.common.duplicate' | translate }}
                        </div>
                    </button>
                </div>
            </mat-tab>
            <mat-tab label="{{ 'store_locator.edit.tabs.style' | translate }}">
                <div class="mt-5 flex flex-col gap-5">
                    <ng-container [ngTemplateOutlet]="warningMessageTemplate"></ng-container>
                    @if (styleFormTemplate()) {
                        <ng-container [ngTemplateOutlet]="styleFormTemplate()!"></ng-container>
                    }
                </div>
            </mat-tab>
        </mat-tab-group>
    </div>
</div>

<ng-template #warningMessageTemplate>
    <div
        class="font-regular flex items-center gap-2 rounded-md bg-malou-color-background-dark px-2 py-2 text-[11px] text-malou-color-text-1">
        <span class="text-[22px]">⚠️</span><span> {{ 'store_locator.edit.common.warning_message' | translate }}</span>
    </div>
</ng-template>
