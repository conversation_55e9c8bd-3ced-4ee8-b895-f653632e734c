import { NgStyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, Signal } from '@angular/core';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

@Component({
    selector: 'app-store-locator-edit-page-cta-block',
    templateUrl: './cta-block.component.html',
    styleUrls: ['./cta-block.component.scss'],
    imports: [NgStyle],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageCtaBlockComponent extends StoreLocatorPageBlockComponent {
    readonly ctaBlockData: Signal<GetStoreLocatorStorePageDto['callToActionsBlock'] | undefined> = computed(
        () => this.editStoreLocatorPageContext.selectedRestaurantStoreLocatorPageUpdate()?.callToActionsBlock
    );

    readonly title = computed(() => this.ctaBlockData()?.title);
    readonly links = computed(() => this.ctaBlockData()?.links);

    constructor() {
        super(StoreLocatorPageBlockType.CALL_TO_ACTION);
    }
}
