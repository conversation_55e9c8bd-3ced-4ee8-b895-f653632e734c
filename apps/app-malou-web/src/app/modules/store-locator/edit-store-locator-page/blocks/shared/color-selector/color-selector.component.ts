import { NgStyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { SelectComponent } from ':shared/components/select/select.component';
import { ApplyPurePipe } from ':shared/pipes/apply-fn.pipe';

@Component({
    selector: 'app-store-locator-edit-page-color-selector',
    templateUrl: './color-selector.component.html',
    styleUrls: ['./color-selector.component.scss'],
    imports: [NgStyle, LazyLoadImageModule, MatIconModule, SelectComponent, ApplyPurePipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageColorSelectorComponent {
    readonly title = input.required<string>();
    readonly colorOptions = input.required<string[]>();
    readonly control = input.required<any>();

    isWhite = (color: string): boolean => color === 'white' || color === '#ffffff';
}
