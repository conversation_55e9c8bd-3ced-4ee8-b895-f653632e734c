<div class="flex items-center justify-between">
    <div class="malou-text-11--bold text-malou-color-text-1">
        {{ title() }}
    </div>
    <app-select class="w-[150px]" [formControl]="control()" [values]="colorOptions()">
        <ng-template let-value="value" #simpleSelectedValueTemplate>
            <div class="flex items-center justify-between">
                <div class="mr-1 flex w-[60px] items-center text-[12px] font-bold text-malou-color-text-1">{{ value }}</div>
                <div
                    class="!h-6 !w-6 rounded-md border-malou-color-text-1"
                    [ngStyle]="{ backgroundColor: value }"
                    [class.border]="isWhite | applyPure: value"></div>
            </div>
        </ng-template>
        <ng-template let-value="value" #optionTemplate>
            <div class="flex items-center justify-start">
                <div
                    class="mr-3 !h-6 !w-6 rounded-md border-malou-color-text-1"
                    [ngStyle]="{ backgroundColor: value }"
                    [class.border]="isWhite | applyPure: value"></div>
                <div class="flex items-center text-malou-color-text-1">{{ value }}</div>
            </div>
        </ng-template>
    </app-select>
</div>
