import { NgStyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, Signal } from '@angular/core';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

@Component({
    selector: 'app-store-locator-edit-page-gallery-block',
    templateUrl: './gallery-block.component.html',
    styleUrls: ['./gallery-block.component.scss'],
    imports: [NgStyle],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageGalleryBlockComponent extends StoreLocatorPageBlockComponent {
    readonly galleryBlockData: Signal<GetStoreLocatorStorePageDto['galleryBlock'] | undefined> = computed(
        () => this.editStoreLocatorPageContext.selectedRestaurantStoreLocatorPageUpdate()?.galleryBlock
    );

    readonly title = computed(() => this.galleryBlockData()?.title);
    readonly subtitle = computed(() => this.galleryBlockData()?.subtitle);
    readonly images = computed(() => this.galleryBlockData()?.images || []);

    constructor() {
        super(StoreLocatorPageBlockType.GALLERY);
    }
}
