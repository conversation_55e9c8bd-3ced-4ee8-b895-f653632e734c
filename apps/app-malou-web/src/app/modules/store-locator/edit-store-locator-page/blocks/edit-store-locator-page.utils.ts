import { GetStoreLocatorOrganizationStyleConfigurationDto } from '@malou-io/package-dto';

import {
    PropertyType,
    StylesConfigurationUpdate,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { ColorHexValue, ExtraColor } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { getFontFamily } from ':modules/store-locator/edit-store-locator-page/utils/inject-font-family';

interface StoreStyleConfiguration {
    colors: GetStoreLocatorOrganizationStyleConfigurationDto['colors'];
    fonts: GetStoreLocatorOrganizationStyleConfigurationDto['fonts'];
}

export function parseConfigurationStyleClassesToCssStyle(classes: string[], design: StoreStyleConfiguration): Record<string, string> {
    const style: Record<string, string> = {};

    for (const className of classes) {
        if (className.startsWith('bg-')) {
            const key = className.replace('bg-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style['backgroundColor'] = value;
            }
        }

        if (className.startsWith('text-')) {
            const key = className.replace('text-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style['color'] = value;
            } else if (key === ExtraColor.WHITE) {
                style['color'] = ColorHexValue.white;
            }
        }

        if (className.startsWith('fill-')) {
            const key = className.replace('fill-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style['fill'] = value;
            }
        }

        if (className.startsWith('border-')) {
            const key = className.replace('border-', '');
            const value = design.colors.find((c: any) => c.class === key)?.value;
            if (value) {
                style['borderColor'] = value;
            }
        }

        if (className.startsWith('font-')) {
            const key = className.replace('font-', '');
            const value = design.fonts.find((f: any) => f.class === key)?.weight;
            if (value) {
                style['fontWeight'] = value;
            }
            if (className.includes('bold')) {
                style['fontFamily'] = getFontFamily(key);
            }
        }

        if (className.startsWith('rounded-')) {
            const key = className.replace('rounded-', '');
            switch (key) {
                case 'full':
                    style['borderRadius'] = '9999px';
                    break;
                case 'lg':
                    style['borderRadius'] = '0.5rem';
                    break;
                case 'md':
                    style['borderRadius'] = '0.375rem';
                    break;
                case 'sm':
                    style['borderRadius'] = '0.125rem';
                    break;
                default:
                    break;
            }
        }
    }

    return style;
}

export function parseColorToConfigurationStyleClass(
    color: string,
    colors: StoreStyleConfiguration['colors'],
    propertyType: PropertyType
): string {
    const colorClass = colors.find((c) => c.value === color)?.class || ExtraColor.WHITE;
    switch (propertyType) {
        case PropertyType.BackgroundColor:
            return `bg-${colorClass}`;
        case PropertyType.Color:
            return `text-${colorClass}`;
        case PropertyType.Fill:
            return `fill-${colorClass}`;
        case PropertyType.BorderColor:
            return `border-${colorClass}`;
        default:
            return '';
    }
}

export function mapUpdateStylesConfiguration(
    organizationStyleConfiguration: GetStoreLocatorOrganizationStyleConfigurationDto,
    newStyles: StylesConfigurationUpdate[]
): Record<string, string[]> {
    const storePageStyles = organizationStyleConfiguration.pages.store || {};
    const storePageColors = organizationStyleConfiguration.colors || [];
    const mappedStylesConfigurationUpdate: Record<string, string[]> = newStyles.reduce((acc, style) => {
        const { elementId, data } = style;
        const classNames = data.map(({ value, propertyType }) => parseColorToConfigurationStyleClass(value, storePageColors, propertyType));
        if (!acc[elementId]) {
            acc[elementId] = [];
        }
        acc[elementId] = updateConfigurationClasses(storePageStyles[elementId], classNames);
        return acc;
    }, {});
    return {
        ...storePageStyles,
        ...mappedStylesConfigurationUpdate,
    };
}

// This function updates the configuration classes by removing old classes that match the new classes' properties
// and adding the new classes. It ensures that the final list contains unique classes.
function updateConfigurationClasses(oldClasses: string[], newClasses: string[]): string[] {
    const newClassesProperties = newClasses.map((attr) => attr.split('-')[0]);
    const classesToKeep = oldClasses.filter((attr) => !newClassesProperties.includes(attr.split('-')[0]));
    return [...classesToKeep, ...newClasses];
}
