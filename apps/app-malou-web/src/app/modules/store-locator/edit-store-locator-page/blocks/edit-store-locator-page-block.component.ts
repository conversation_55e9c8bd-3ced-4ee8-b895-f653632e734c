import { ChangeDetectionStrategy, Component, computed, inject, output, signal, Signal, WritableSignal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { GetStoreLocatorOrganizationStyleConfigurationDto } from '@malou-io/package-dto';
import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { ToastService } from ':core/services/toast.service';
import { parseConfigurationStyleClassesToCssStyle } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page.utils';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';

@Component({
    selector: 'app-edit-store-locator-page-block',
    template: '',
    imports: [MatButtonModule, MatTabsModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageBlockComponent {
    readonly emitChangeFormBlock = output<StoreLocatorPageBlockType>();

    readonly _translate = inject(TranslateService);
    readonly editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    readonly toastService = inject(ToastService);

    readonly StoreLocatorPageBlockType = StoreLocatorPageBlockType;
    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;
    readonly StoreLocatorRestaurantPageElementIds = StoreLocatorRestaurantPageElementIds;

    readonly organizationStyleConfiguration: Signal<GetStoreLocatorOrganizationStyleConfigurationDto | null> = computed(() =>
        this.editStoreLocatorPageContext.organizationStyleConfiguration()
    );

    readonly blockType: WritableSignal<StoreLocatorPageBlockType> = signal(StoreLocatorPageBlockType.INFORMATION);
    readonly shouldShowCursorNotAllowed = computed(
        () =>
            this.editStoreLocatorPageContext.isBlockInError().blockType !== this.blockType() &&
            this.editStoreLocatorPageContext.isBlockInError().isError
    );

    constructor(blockType: StoreLocatorPageBlockType) {
        this.blockType.set(blockType);
    }

    handleShowFormBlock(blockType: StoreLocatorPageBlockType): void {
        if (!this.editStoreLocatorPageContext.isBlockInError().isError) {
            this.emitChangeFormBlock.emit(blockType);
            return;
        }
        this.toastService.openErrorToast(this._translate.instant('store-locator.edit-page.common.block-in-error'));
    }

    getStyleByKey(key: StoreLocatorRestaurantPageElementIds): Record<string, string> {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!organizationStyleConfiguration) {
            return {};
        }
        const informationBlockElementStyleClasses = organizationStyleConfiguration.pages?.store[key];
        if (informationBlockElementStyleClasses) {
            return parseConfigurationStyleClassesToCssStyle(informationBlockElementStyleClasses, organizationStyleConfiguration);
        }
        return {};
    }
}
