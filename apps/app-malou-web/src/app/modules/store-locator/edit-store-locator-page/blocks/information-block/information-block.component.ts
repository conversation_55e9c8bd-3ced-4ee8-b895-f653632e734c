import { NgStyle } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, signal, Signal, WritableSignal } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';
import { Day } from '@malou-io/package-utils';

import { StoreLocatorPageBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { getFontFamily } from ':modules/store-locator/edit-store-locator-page/utils/inject-font-family';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-store-locator-edit-page-information-block',
    templateUrl: './information-block.component.html',
    styleUrls: ['./information-block.component.scss'],
    imports: [NgStyle, LazyLoadImageModule, ImagePathResolverPipe, MatIconModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageInformationBlockComponent extends StoreLocatorPageBlockComponent {
    readonly informationBlockData: Signal<GetStoreLocatorStorePageDto['informationBlock'] | undefined> = computed(
        () => this.editStoreLocatorPageContext.selectedRestaurantStoreLocatorPageUpdate()?.informationBlock
    );

    readonly informationFormattedHours: WritableSignal<{ value: string; extraStyle: any }[]> = signal([]);

    readonly primaryColor = computed(
        () => this.organizationStyleConfiguration()?.colors.find((color) => color.class === 'primary')?.value || '#000000'
    );
    readonly image = computed(() => ({
        src: this.informationBlockData()?.imageUrl,
        alt: 'image-block',
    }));
    readonly isNotOpenedYet = computed(() => this.informationBlockData()?.isNotOpenedYet);
    readonly restaurantName = computed(() => this.informationBlockData()?.restaurantName);
    readonly itineraryUrl = computed(() => this.informationBlockData()?.itineraryUrl);
    readonly fullAddress = computed(() => this.informationBlockData()?.fullAddress);
    readonly phoneNumber = computed(() => this.informationBlockData()?.phone);
    readonly informationAttributes = computed(() => this.informationBlockData()?.attributesNames.join(', ') || []);
    readonly informationPaymentMethods = computed(() => this.informationBlockData()?.paymentMethods.join(', ') || []);
    readonly informationCta = computed(() => this.informationBlockData()?.cta);

    constructor() {
        super(StoreLocatorPageBlockType.INFORMATION);
        this.informationFormattedHours.set(this._getHoursSection(this.informationBlockData()?.hours || []));
    }

    private _getHoursSection(hours: GetStoreLocatorStorePageDto['informationBlock']['hours']): { value: string; extraStyle: any }[] {
        const today = new Date();
        const dayOfWeek = today.getDay();
        const fontFamilyClass = 'primary-bold';

        const todaySchedule = hours[dayOfWeek - 1];
        const tomorrowSchedule = hours[dayOfWeek];

        const scheduleOfToday = hours.find((h) => h.day === todaySchedule!.day);

        const textNow = this._getFormattedScheduleOfTheDayOpenOrSoonOpen(scheduleOfToday!.periods, todaySchedule!.day as Day, today, false);

        const scheduleOfTomorrow = hours.find((h) => h.day === tomorrowSchedule!.day);
        const textTomorrow = this._getFormattedScheduleOfTheDayOpenOrSoonOpen(
            scheduleOfTomorrow!.periods,
            tomorrowSchedule!.day as Day,
            today,
            true
        );
        // need to sort the days by starting with the current day
        const scheduleSortedByStartingTheCurrentDay = hours
            .slice(new Date().getDay() - 1, hours.length)
            .concat(hours.slice(0, new Date().getDay() - 1));

        return scheduleSortedByStartingTheCurrentDay.map((h, idx) => {
            if (idx === 0) {
                return {
                    value: textNow,
                    extraStyle:
                        textNow.includes('Ouvert maintenant') || textNow.includes('Ouvre bientôt')
                            ? { fontFamily: getFontFamily(fontFamilyClass) }
                            : {},
                };
            }
            if (idx === 1) {
                return {
                    value: textTomorrow,
                    extraStyle:
                        textTomorrow.includes('Ouvert maintenant') || textTomorrow.includes('Ouvre bientôt')
                            ? { fontFamily: getFontFamily(fontFamilyClass) }
                            : {},
                };
            }
            return {
                value: h.formattedHour,
                extraStyle:
                    h.formattedHour.includes('Ouvert maintenant') || h.formattedHour.includes('Ouvre bientôt')
                        ? { fontFamily: getFontFamily(fontFamilyClass) }
                        : {},
            };
        });
    }

    private _getFormattedScheduleOfTheDayOpenOrSoonOpen(
        hours: GetStoreLocatorStorePageDto['informationBlock']['hours'][0]['periods'],
        day: Day,
        now: Date,
        isTomorrow: boolean
    ): string {
        return (
            this._translate.instant(`enums.days.${day.toLowerCase()}`) +
            ' : ' +
            hours
                .map((p) => {
                    if (p.isClosed) {
                        return 'Fermé';
                    }

                    const nowOrTomorrow = new Date().setDate(now.getDate() + (isTomorrow ? 1 : 0));

                    const openTimeHours = p.openTime!.split(':')[0];
                    const openTimeMinutes = p.openTime!.split(':')[1];
                    const closeTimeHours = p.closeTime!.split(':')[0];
                    const closeTimeMinutes = p.closeTime!.split(':')[1];

                    const openTimeDate = new Date(new Date(nowOrTomorrow).setHours(+openTimeHours!, +openTimeMinutes!));
                    const closeTimeDate = new Date(new Date(nowOrTomorrow).setHours(+closeTimeHours!, +closeTimeMinutes!));

                    if (now >= openTimeDate && now <= closeTimeDate) {
                        return `Ouvert maintenant - ${p.closeTime}`;
                    }

                    const openTimeDateForTomorrow = new Date(new Date(nowOrTomorrow).setHours(+openTimeHours!, +openTimeMinutes!));

                    // need to do this because setHours is mutating the date
                    const openTimeDateForTomorrow2 = new Date(new Date(nowOrTomorrow).setHours(+openTimeHours!, +openTimeMinutes!));

                    if (
                        now >= new Date(openTimeDateForTomorrow.setHours(openTimeDateForTomorrow.getHours() - 1)) &&
                        now < openTimeDateForTomorrow2
                    ) {
                        return `Ouvre bientôt ${p.openTime} - ${p.closeTime}`;
                    }

                    return `${p.openTime} - ${p.closeTime}`;
                })
                .join(', ')
        );
    }
}
