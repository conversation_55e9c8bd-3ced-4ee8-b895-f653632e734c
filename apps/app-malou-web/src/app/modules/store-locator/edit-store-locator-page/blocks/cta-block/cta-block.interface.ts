import { FormArray, FormControl, FormGroup } from '@angular/forms';

export interface CtaBlockContentFormData {
    title: string;
    ctaButtons: {
        text: string;
        url: string;
    }[];
}

export type CtaButtonsFormGroup = FormGroup<{
    text: FormControl<string>;
    url: FormControl<string>;
}>;

export interface CtaBlockContentForm {
    title: FormControl<string>;
    ctaButtons: FormArray<CtaButtonsFormGroup>;
}

export interface CtaBlockStyleData {
    general: {
        backgroundColor: string;
        titleColor: string;
    };
    buttons: {
        textColor: string;
        backgroundColor: string;
        borderColor: string;
    };
}

export interface CtaBlockStyleForm {
    general: FormGroup<{
        backgroundColor: FormControl<string>;
        titleColor: FormControl<string>;
    }>;
    buttons: FormGroup<{
        textColor: FormControl<string>;
        backgroundColor: FormControl<string>;
        borderColor: FormControl<string>;
    }>;
}

export enum CtaBlockContentFormInputValidation {
    TITLE_MIN_LENGTH = 5,
    TITLE_MAX_LENGTH = 60,
    CTA_TEXT_MIN_LENGTH = 5,
    CTA_TEXT_MAX_LENGTH = 20,
    CTA_URL_MIN_LENGTH = 3,
}
