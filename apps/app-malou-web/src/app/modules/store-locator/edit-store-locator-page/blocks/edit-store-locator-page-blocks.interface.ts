import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

export enum PropertyType {
    BackgroundColor = 'backgroundColor',
    Color = 'color',
    Fill = 'fill',
    BorderColor = 'borderColor',
}

export interface StylesConfigurationUpdate {
    elementId: StoreLocatorRestaurantPageElementIds;
    data: {
        value: string;
        propertyType: PropertyType;
    }[];
}
