<app-edit-store-locator-page-block-form-wrapper [contentFormTemplate]="contentFormTemplate" [styleFormTemplate]="styleFormTemplate" />

<ng-template #contentFormTemplate>
    <div class="flex flex-col gap-5" [formGroup]="contentForm">
        <app-input-text
            class="malou-text-14--bold!"
            formControlName="title"
            [defaultValue]="title()"
            [title]="'store_locator.edit.controls.title.name' | translate"
            [disabled]="false"
            [errorMessage]="
                titleControl?.errors && (titleControl?.errors?.minlength || titleControl?.errors?.maxlength)
                    ? ('store_locator.edit.controls.title.length_error' | translate)
                    : ''
            "
            [isEmojiPickerEnabled]="true"
            [placeholder]="'store_locator.edit.controls.title.placeholder' | translate"
            [autocapitalize]="'none'">
        </app-input-text>
        <div class="w-fit">
            @if (selectedRestaurant()) {
                <app-image-uploader
                    titleClass="!malou-text-14--bold !text-malou-color-text-1"
                    [(media)]="uploadedMedia"
                    [acceptedMimeTypes]="[MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG]"
                    [restaurant]="selectedRestaurant()!"
                    [title]="'store_locator.edit.controls.image_upload.name' | translate"></app-image-uploader>
            }
        </div>

        <div class="expansion-header malou-expansion-panel">
            <mat-accordion>
                <mat-expansion-panel class="!border-none" hideToggle [expanded]="false">
                    <mat-expansion-panel-header class="!pl-0" (click)="isButtonsPanelExpanded.set(!isButtonsPanelExpanded())">
                        <div class="flex w-full items-center justify-between">
                            <div class="malou-text-13--bold text-malou-color-text-1">
                                {{ 'store_locator.edit.controls.cta.header' | translate }}
                            </div>
                            <div class="flex items-center">
                                <mat-icon
                                    class="!w-3 transition-all"
                                    color="primary"
                                    [svgIcon]="SvgIcon.CHEVRON_DOWN"
                                    [class.rotate-180]="isButtonsPanelExpanded()"></mat-icon>
                            </div>
                        </div>
                    </mat-expansion-panel-header>

                    <ng-template matExpansionPanelContent>
                        <div class="flex flex-col gap-4">
                            <div class="flex flex-col gap-2 !bg-malou-color-background-light" formGroupName="cta">
                                <div class="flex items-center">
                                    <app-slide-toggle class="mr-3" [checked]="isCtaEnabled()" (onToggle)="onCtaToggle()">
                                    </app-slide-toggle>
                                    <div class="font-regular text-[12px] text-malou-color-text-1">
                                        {{ 'store_locator.edit.controls.cta.primary_button' | translate }}
                                    </div>
                                </div>
                                <app-input-text
                                    class="malou-text-14--bold!"
                                    formControlName="text"
                                    [placeholder]="'store_locator.edit.controls.cta.title' | translate"
                                    [autocapitalize]="'none'">
                                </app-input-text>
                                <app-input-text
                                    class="malou-text-14--bold!"
                                    formControlName="url"
                                    [placeholder]="'store_locator.edit.controls.cta.placeholder' | translate"
                                    [autocapitalize]="'none'">
                                </app-input-text>
                            </div>
                        </div>
                    </ng-template>
                </mat-expansion-panel>
            </mat-accordion>
        </div>
    </div>
</ng-template>

<ng-template #styleFormTemplate>
    <form class="flex flex-col gap-5" [formGroup]="styleForm">
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="general">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit.style.general' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.backgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.title' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.titleColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.textColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.icon' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('general.iconColor')" />
        </div>
        <div class="flex flex-col gap-2 rounded-[10px] bg-white p-4" formGroupName="buttons">
            <div class="malou-text-16--bold text-malou-color-text-1">
                {{ 'store_locator.edit.style.buttons.title' | translate }}
            </div>
            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.buttons.primary_background' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryBackgroundColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.buttons.primary_border' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryBorderColor')" />

            <app-store-locator-edit-page-color-selector
                [title]="'store_locator.edit.style.buttons.primary_text' | translate"
                [colorOptions]="colorOptions()"
                [control]="styleForm.get('buttons.primaryTextColor')" />
        </div>
    </form>
</ng-template>
