import { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';

// GENERAL
export enum StoreLocatorPageBlockType {
    INFORMATION = 'information',
    GALLERY = 'gallery',
    REVIEWS = 'reviews',
    SOCIAL_MEDIA = 'social_media',
    CALL_TO_ACTION = 'call_to_action',
    TEXT = 'text',
}

export enum ExtraColor {
    WHITE = 'white',
}

export const ColorHexValue = {
    white: '#ffffff',
};

export const EXTRA_COLORS = [ColorHexValue.white];

export type StoreLocatorPageUpdatePerStore = Record<string, GetStoreLocatorStorePageDto>;

// BLOCS

export interface CallToActionButton {
    label: string;
    url: string;
}
