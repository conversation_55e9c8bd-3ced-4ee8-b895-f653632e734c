import { computed, effect, inject, Injectable, Signal, signal, WritableSignal } from '@angular/core';
import { map, Observable } from 'rxjs';

import { GetStoreLocatorOrganizationStyleConfigurationDto, GetStoreLocatorStorePageDto } from '@malou-io/package-dto';

import {
    StoreLocatorPageBlockType,
    StoreLocatorPageUpdatePerStore,
} from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { loadDynamicFont } from ':modules/store-locator/edit-store-locator-page/utils/inject-font-family';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';
import { Restaurant } from ':shared/models';

@Injectable({
    providedIn: 'root',
})
export class EditStoreLocatorPageContext {
    private readonly _storeLocatorService = inject(StoreLocatorService);

    readonly stores: WritableSignal<GetStoreLocatorStorePageDto[]> = signal([]);
    readonly organizationRestaurants: WritableSignal<Restaurant[]> = signal([]);
    readonly organizationName: WritableSignal<string> = signal('');
    readonly organizationStyleConfiguration: WritableSignal<GetStoreLocatorOrganizationStyleConfigurationDto | null> = signal(null);

    readonly isBlockInError: WritableSignal<{
        blockType: StoreLocatorPageBlockType;
        isError: boolean;
    }> = signal({
        blockType: StoreLocatorPageBlockType.INFORMATION,
        isError: false,
    });

    readonly currentEditingRestaurant: WritableSignal<Restaurant | null> = signal(null);
    readonly selectedRestaurantStoreLocatorPageConfig: WritableSignal<GetStoreLocatorStorePageDto | null> = signal(null);

    readonly storesToUpdate: WritableSignal<StoreLocatorPageUpdatePerStore> = signal({});
    readonly selectedRestaurantStoreLocatorPageUpdate: Signal<StoreLocatorPageUpdatePerStore[0] | null> = computed(() => {
        const currentRestaurantId: string | undefined = this.currentEditingRestaurant()?.id;
        if (!currentRestaurantId) {
            return null;
        }
        return this.storesToUpdate()[currentRestaurantId] ?? null;
    });

    constructor() {
        effect(() => {
            const organizationName = this.currentEditingRestaurant()?.organization?.name;
            const organizationStyle = this.organizationStyleConfiguration();
            if (organizationStyle && organizationName) {
                loadDynamicFont(organizationStyle.fonts);
            }
        });
    }

    getStores(organizationId: string): Observable<GetStoreLocatorStorePageDto[] | null> {
        return this._storeLocatorService.getStores(organizationId).pipe(map((res) => res.data));
    }

    getOrganizationStyleConfiguration(organizationId: string): Observable<GetStoreLocatorOrganizationStyleConfigurationDto | null> {
        return this._storeLocatorService.getOrganizationStyleConfiguration(organizationId).pipe(
            map((res) => {
                this.organizationStyleConfiguration.set(res.data);
                return this.organizationStyleConfiguration();
            })
        );
    }

    updateCurrentEditingRestaurant(restaurant: Restaurant): void {
        const storeLocatorPageUpdate = this.stores().find((store) => store.id === restaurant.id);
        if (!storeLocatorPageUpdate) {
            console.error('Store locator page update not found for the selected restaurant:');
            return;
        }
        this.selectedRestaurantStoreLocatorPageConfig.set(storeLocatorPageUpdate);
        this._getOrCreateStoreLocatorPageUpdatePerStore(restaurant.id);
        this.currentEditingRestaurant.set(restaurant);
    }

    updateInformationBlock(updatedInformationBlock: GetStoreLocatorStorePageDto['informationBlock']): void {
        const currentRestaurantId: string | undefined = this.currentEditingRestaurant()?.id;
        if (!currentRestaurantId) {
            return;
        }
        const storeLocatorPageUpdate = this._getOrCreateStoreLocatorPageUpdatePerStore(currentRestaurantId);
        if (!storeLocatorPageUpdate) {
            console.error('Failed to create or retrieve store locator page update for the current restaurant.');
            return;
        }
        this.storesToUpdate.update((prev) => ({
            ...prev,
            [currentRestaurantId]: {
                ...storeLocatorPageUpdate,
                informationBlock: updatedInformationBlock,
            },
        }));
    }

    updateGalleryBlock(updatedGalleryBlock: GetStoreLocatorStorePageDto['galleryBlock']): void {
        const currentRestaurantId: string | undefined = this.currentEditingRestaurant()?.id;
        if (!currentRestaurantId) {
            return;
        }
        const storeLocatorPageUpdate = this._getOrCreateStoreLocatorPageUpdatePerStore(currentRestaurantId);
        if (!storeLocatorPageUpdate) {
            console.error('Failed to create or retrieve store locator page update for the current restaurant.');
            return;
        }
        this.storesToUpdate.update((prev) => ({
            ...prev,
            [currentRestaurantId]: {
                ...storeLocatorPageUpdate,
                galleryBlock: updatedGalleryBlock,
            },
        }));
    }

    updateCallToActionsBlock(updatedCallToActionBlock: GetStoreLocatorStorePageDto['callToActionsBlock']): void {
        const currentRestaurantId: string | undefined = this.currentEditingRestaurant()?.id;
        if (!currentRestaurantId) {
            return;
        }
        const storeLocatorPageUpdate = this._getOrCreateStoreLocatorPageUpdatePerStore(currentRestaurantId);
        if (!storeLocatorPageUpdate) {
            console.error('Failed to create or retrieve store locator page update for the current restaurant.');
            return;
        }
        this.storesToUpdate.update((prev) => ({
            ...prev,
            [currentRestaurantId]: {
                ...storeLocatorPageUpdate,
                callToActionsBlock: updatedCallToActionBlock,
            },
        }));
    }

    updateStyle(updatedStyle: Record<string, string[]>): void {
        const currentRestaurantId: string | undefined = this.currentEditingRestaurant()?.id;
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        if (!currentRestaurantId || !organizationStyleConfiguration) {
            return;
        }
        this.organizationStyleConfiguration.update(() => ({
            colors: organizationStyleConfiguration.colors,
            fonts: organizationStyleConfiguration.fonts,
            pages: {
                store: updatedStyle,
            },
        }));
    }

    private _getOrCreateStoreLocatorPageUpdatePerStore(currentRestaurantId: string): StoreLocatorPageUpdatePerStore[0] | null {
        const storeLocatorPageUpdate = this.storesToUpdate()[currentRestaurantId];
        if (!storeLocatorPageUpdate) {
            const selectedStorePage = this.selectedRestaurantStoreLocatorPageConfig();
            if (!selectedStorePage) {
                return null;
            }
            const storeLocatorPage: StoreLocatorPageUpdatePerStore = {
                [currentRestaurantId]: selectedStorePage,
            };
            this.storesToUpdate.update((prev) => ({ ...prev, ...storeLocatorPage }));
            return storeLocatorPage[currentRestaurantId];
        }
        return storeLocatorPageUpdate;
    }
}
