import { <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Store } from '@ngrx/store';
import { combineLatest, filter, Observable, of, switchMap, throwError } from 'rxjs';

import { isNotNil } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { selectOwnRestaurants } from ':modules/restaurant-list/restaurant-list.reducer';
import { StoreLocatorPageCtaBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/cta-block/cta-block.component';
import { StoreLocatorPageCtaBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/cta-block/form/cta-block-form.component';
import { StoreLocatorPageGalleryBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/gallery-block/form/gallery-block-form.component';
import { StoreLocatorPageGalleryBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/gallery-block/gallery-block.component';
import { StoreLocatorPageInformationBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/information-block/form/information-block-form.component';
import { StoreLocatorPageInformationBlockComponent } from ':modules/store-locator/edit-store-locator-page/blocks/information-block/information-block.component';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { getFontFamily } from ':modules/store-locator/edit-store-locator-page/utils/inject-font-family';
import { Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-store-locator-edit-page',
    templateUrl: './edit-store-locator-page.component.html',
    styleUrls: ['./edit-store-locator-page.component.scss'],
    imports: [
        MatButtonModule,
        MatProgressSpinnerModule,
        NgTemplateOutlet,
        StoreLocatorPageInformationBlockComponent,
        StoreLocatorPageInformationBlockFormComponent,
        MatIconModule,
        NgStyle,
        StoreLocatorPageGalleryBlockFormComponent,
        StoreLocatorPageGalleryBlockComponent,
        StoreLocatorPageCtaBlockFormComponent,
        StoreLocatorPageCtaBlockComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorPageModalComponent implements OnInit {
    private readonly _store = inject(Store);
    private readonly _editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    private readonly _dialogRef = inject(MatDialogRef<EditStoreLocatorPageModalComponent>);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _destroyRef = inject(DestroyRef);

    readonly SvgIcon = SvgIcon;
    readonly StoreLocatorPageBlockType = StoreLocatorPageBlockType;

    readonly selectedRestaurant$: Observable<Restaurant | null> = this._restaurantsService.restaurantSelected$;
    readonly ownedRestaurant$: Observable<Restaurant[]> = this._store.select(selectOwnRestaurants);

    readonly selectedBlock: WritableSignal<StoreLocatorPageBlockType> = signal(StoreLocatorPageBlockType.INFORMATION);

    readonly isLoading: WritableSignal<boolean> = signal(true);

    readonly textFontFamilyClass = computed(() => getFontFamily('primary'));

    ngOnInit(): void {
        this._initEditPage();
    }

    closeEditModal(): void {
        this._dialogRef.close();
    }

    handleShowFormBlock(blockType: StoreLocatorPageBlockType): void {
        this.selectedBlock.set(blockType);
    }

    private _initEditPage(): void {
        this.selectedRestaurant$
            .pipe(
                filter(isNotNil),
                switchMap((selectedRestaurant) => {
                    this.isLoading.set(true);
                    if (!selectedRestaurant.organizationId) {
                        return throwError(() => new Error('Selected restaurant does not have an organization ID'));
                    }
                    return combineLatest([
                        of(selectedRestaurant),
                        this._restaurantsService.getOrganizationRestaurants(selectedRestaurant.organizationId),
                        this._editStoreLocatorPageContext.getStores(selectedRestaurant.organizationId),
                        this._editStoreLocatorPageContext.getOrganizationStyleConfiguration(selectedRestaurant.organizationId),
                    ]);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: ([selectedRestaurant, organizationRestaurants, stores]) => {
                    this._editStoreLocatorPageContext.currentEditingRestaurant.set(selectedRestaurant);
                    this._editStoreLocatorPageContext.organizationName.set(selectedRestaurant.organization?.name ?? '');
                    this._editStoreLocatorPageContext.organizationRestaurants.set(organizationRestaurants);
                    this._editStoreLocatorPageContext.stores.set(stores || []);
                    const selectedRestaurantStore = stores?.find((store) => store.id === selectedRestaurant.id);
                    if (selectedRestaurantStore) {
                        this._editStoreLocatorPageContext.selectedRestaurantStoreLocatorPageConfig.set(selectedRestaurantStore);
                        this._editStoreLocatorPageContext.storesToUpdate.set({
                            [selectedRestaurant.id]: selectedRestaurantStore,
                        });
                    }
                    this.isLoading.set(false);
                },
                error: (error) => {
                    console.error('Error init:', error);
                    this.isLoading.set(false);
                    this._dialogRef.close();
                },
            });
    }
}
