@if (isLoading()) {
    <div class="flex h-full items-center justify-center">
        <mat-spinner diameter="60"></mat-spinner>
    </div>
} @else {
    <div class="flex h-full">
        <div class="h-full w-1/4 border-r border-malou-color-background-dark bg-malou-color-background-light px-5 py-3">
            <ng-container [ngTemplateOutlet]="editBlockFormTemplate"></ng-container>
        </div>
        <div class="flex w-3/4 flex-col gap-y-10">
            <div class="flex w-full items-center border-b border-malou-color-background-dark px-5 py-3">
                <div class="flex w-1/2 justify-end gap-x-1">
                    <button class="malou-btn-icon--secondary btn-sm" mat-icon-button>
                        <mat-icon color="primary" [svgIcon]="SvgIcon.DESKTOP_DISPLAY"></mat-icon>
                    </button>
                    <button class="malou-btn-icon--secondary btn-sm" mat-icon-button>
                        <mat-icon color="primary" [svgIcon]="SvgIcon.PHONE_DISPLAY"></mat-icon>
                    </button>
                </div>
                <div class="flex w-1/2 justify-end gap-3">
                    <button class="malou-btn-flat text-sm font-extrabold text-malou-color-text-1" mat-button (click)="closeEditModal()">
                        Annuler
                    </button>
                    <button class="malou-btn-raised--primary w-[150px]" mat-raised-button>Brouillon</button>
                </div>
            </div>
            <div class="flex h-[100vh] w-full flex-col overflow-scroll px-3 pb-12" [ngStyle]="{ fontFamily: textFontFamilyClass() }">
                <app-store-locator-edit-page-information-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-gallery-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
                <app-store-locator-edit-page-cta-block (emitChangeFormBlock)="handleShowFormBlock($event)" />
            </div>
        </div>
    </div>
}

<ng-template #editBlockFormTemplate>
    @switch (selectedBlock()) {
        @case (StoreLocatorPageBlockType.INFORMATION) {
            <app-store-locator-edit-page-information-block-form />
        }
        @case (StoreLocatorPageBlockType.GALLERY) {
            <app-store-locator-edit-page-gallery-block-form />
        }
        @case (StoreLocatorPageBlockType.CALL_TO_ACTION) {
            <app-store-locator-edit-page-cta-block-form />
        }
    }
</ng-template>
