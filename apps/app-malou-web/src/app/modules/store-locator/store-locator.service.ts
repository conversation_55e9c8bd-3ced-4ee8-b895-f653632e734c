/* eslint-disable @typescript-eslint/no-unused-vars */
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import {
    GetStoreLocatorOrganizationStyleConfigurationDto,
    GetStoreLocatorStorePageDto,
    StoreLocatorOrganizationConfigurationResponseDto,
    UpdateOrganizationConfigurationAiSettingsBodyDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { environment } from ':environments/environment';
import { StoreLocatorOrganizationConfiguration } from ':shared/models/store-locator-organization-config';

export interface RoiInsightsCreationState {
    wasLastResultSeen: boolean;
    creationStartDate: Date | null;
    creationEstimatedTime: number;
}

@Injectable({
    providedIn: 'root',
})
export class StoreLocatorService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/store-locator`;

    private readonly _http = inject(HttpClient);

    getStores(organizationId: string): Observable<ApiResultV2<GetStoreLocatorStorePageDto[]>> {
        return this._http.get<ApiResultV2<GetStoreLocatorStorePageDto[]>>(`${this.API_BASE_URL}/${organizationId}/all-stores`);
    }

    getOrganizationStyleConfiguration(organizationId: string): Observable<ApiResultV2<GetStoreLocatorOrganizationStyleConfigurationDto>> {
        return this._http.get<ApiResultV2<any>>(`${this.API_BASE_URL}/${organizationId}/style-configuration`);
    }

    getOrganizationConfiguration(organizationId: string): Observable<StoreLocatorOrganizationConfiguration> {
        return this._http
            .get<
                ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration`)
            .pipe(map((data) => StoreLocatorOrganizationConfiguration.fromDto(data.data)));
    }

    updateOrganizationConfigurationAiSettings(
        organizationId: string,
        params: UpdateOrganizationConfigurationAiSettingsBodyDto
    ): Observable<StoreLocatorOrganizationConfiguration> {
        return this._http
            .put<
                ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration/ai-settings`, params)
            .pipe(map((data) => StoreLocatorOrganizationConfiguration.fromDto(data.data)));
    }
}
