<div class="px-20 pt-10">
    <ng-container [ngTemplateOutlet]="isLoading() ? loadingTemplate : storeLocatorTemplate"> </ng-container>
</div>

<ng-template #loadingTemplate>
    <div class="flex h-screen w-full items-center justify-center">
        <mat-spinner diameter="50"></mat-spinner>
    </div>
</ng-template>

<ng-template #storeLocatorTemplate>
    @if (false) {
        <div class="malou-card container m-8">
            <div class="flex flex-col gap-2">
                <div class="malou-title__kevin malou-text--2x-large malou-text--semi-bold">{{ 'store_locator.title' | translate }}</div>
                <button class="malou-btn-raised--primary w-[300px]" mat-raised-button (click)="editStoreLocatorPage()">
                    {{ 'common.edit' | translate }}
                </button>
            </div>
        </div>
    } @else {
        <ng-container [ngTemplateOutlet]="organizationConfigurationTemplate"></ng-container>
    }
</ng-template>

<ng-template #organizationConfigurationTemplate>
    <div class="malou-card container m-8">
        <div class="flex flex-col gap-8">
            <div class="flex flex-col">
                <div class="malou-text-20--bold malou-color-text-1">{{ 'store_locator.configuration.title' | translate }}</div>
                <div class="malou-text-10--regular malou-color-text-2 italic">{{ 'store_locator.configuration.subtitle' | translate }}</div>
            </div>

            <div class="flex flex-row justify-around lg:flex-col">
                <div class="flex">
                    <img class="h-96" alt="Calque illustration" [src]="Illustration.Calque | illustrationPathResolver" />
                </div>

                <div class="flex flex-col justify-center gap-4">
                    <div class="flex items-center gap-4">
                        <ng-container
                            *ngTemplateOutlet="
                                stateCheckTemplate;
                                context: { isChecked: storeLocatorOrganizationConfiguration().isStylesConfigured() }
                            ">
                        </ng-container>
                        <div
                            class="malou-border-color-dark flex grow cursor-pointer items-center justify-between gap-4 rounded-lg border px-6 py-8">
                            <div class="flex h-8 w-8 items-center justify-center rounded-full bg-malou-color-background-dark">
                                <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.PADLOCK"></mat-icon>
                            </div>
                            <div class="flex flex-col">
                                <span class="malou-text-16--bold malou-color-text-1">
                                    {{ 'store_locator.configuration.graphic_charter.title' | translate }}
                                </span>
                                <span class="malou-text-10--regular malou-color-text-2 italic">
                                    {{ 'store_locator.configuration.graphic_charter.description' | translate }}
                                </span>
                            </div>
                            <mat-icon class="!h-4 !w-4 cursor-pointer" color="primary" [svgIcon]="SvgIcon.ARROW_RIGHT"></mat-icon>
                        </div>
                    </div>

                    <div class="flex items-center gap-4">
                        <ng-container
                            *ngTemplateOutlet="
                                stateCheckTemplate;
                                context: { isChecked: storeLocatorOrganizationConfiguration().isAiSettingsConfigured() }
                            ">
                        </ng-container>
                        <div
                            class="malou-border-color-dark flex grow cursor-pointer items-center justify-between gap-4 rounded-lg border px-6 py-8"
                            (click)="openEditAiSettingsModal()">
                            <div class="flex h-8 w-8 items-center justify-center rounded-full bg-malou-color-background-dark">
                                <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.MAGIC_WAND"></mat-icon>
                            </div>
                            <div class="flex flex-col">
                                <span class="malou-text-16--bold malou-color-text-1">
                                    {{ 'store_locator.configuration.ai_assistant.title' | translate }}
                                </span>
                                <span class="malou-text-10--regular malou-color-text-2 italic">
                                    {{ 'store_locator.configuration.ai_assistant.description' | translate }}
                                </span>
                            </div>
                            <mat-icon class="!h-4 !w-4 cursor-pointer" color="primary" [svgIcon]="SvgIcon.ARROW_RIGHT"></mat-icon>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template let-isChecked="isChecked" #stateCheckTemplate>
    @if (isChecked) {
        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-malou-color-state-success--light/50 p-2">
            <mat-icon class="text-malou-color-background-success-dark" [svgIcon]="SvgIcon.CHECK"></mat-icon>
        </div>
    } @else {
        <div class="flex h-10 w-10 items-center justify-center rounded-full bg-malou-color-state-error/20 p-2">
            <mat-icon class="text-malou-color-state-error" [svgIcon]="SvgIcon.CROSS"></mat-icon>
        </div>
    }
</ng-template>
