import { AbstractControl, ValidatorFn } from '@angular/forms';

import { isValidUrl } from '@malou-io/package-utils';

export function UrlValidator(allowEmpty: boolean = false): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
        if (control.pristine) {
            return null;
        }
        const isValid = isValidUrl(control.value, { allowEmpty });
        return isValid ? null : { invalidUrl: true };
    };
}
