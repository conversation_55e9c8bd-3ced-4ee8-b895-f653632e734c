import { StoreLocatorOrganizationRestaurantDto } from '@malou-io/package-dto';
import { PlatformKey } from '@malou-io/package-utils';

import { Address } from './address';

export interface StoreLocatorAttribute {
    restaurantAttributeId: string;
    dbAttributeId: string;
    restaurantId: string;
    attributeId: string;
    platformKey: PlatformKey;
    attributeName: {
        fr: string;
        en?: string;
        es?: string;
        it?: string;
    };
}

export class StoreLocatorOrganizationRestaurant {
    id: string;
    name: string;
    internalName?: string;
    address?: Address;
    attributeList?: StoreLocatorAttribute[];

    constructor(init: Partial<StoreLocatorOrganizationRestaurant> = {}) {
        this.id = init.id || '';
        this.name = init.name || '';
        this.internalName = init.internalName;
        this.address = init.address ? new Address(init.address) : undefined;
        this.attributeList = init.attributeList;
    }

    static fromDto(dto: StoreLocatorOrganizationRestaurantDto): StoreLocatorOrganizationRestaurant {
        return new StoreLocatorOrganizationRestaurant({
            id: dto.id,
            name: dto.name,
            internalName: dto.internalName,
            address: dto.address ? new Address(dto.address) : undefined,
            attributeList: dto.attributeList,
        });
    }

    getAttributeNameForLang(restaurantAttributeId: string, lang: string): string {
        const resturantAttribute = this.attributeList?.find((att) => att.restaurantAttributeId === restaurantAttributeId);
        if (!resturantAttribute) {
            return '';
        }

        return resturantAttribute.attributeName[lang] || resturantAttribute.attributeName.fr || '';
    }

    getDisplayName(): string {
        return this.internalName || this.name;
    }

    hasCategories(): boolean {
        return Boolean(this.attributeList && this.attributeList.length > 0);
    }
}
