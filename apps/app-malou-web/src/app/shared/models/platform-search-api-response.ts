import { ConnectableStatus } from '@malou-io/package-utils';

export interface PlatformSearchApiResponse {
    list: PlatformSearch[];
    pagination?: SocialIdsPagination;
}

export interface PermissionsState {
    isValid: boolean;
    missing: string[];
    dataExpiresAt?: number;
}

export interface PlatformSearch {
    name: string;
    locationId: string;
    formattedAddress: string;
    picture: string;
    rating: number;
    socialUrl: string;
    socialId: string;
    access?: PermissionsState;
    accountId?: string;
    accountName?: string;
    apiEndpointV2?: string;
    apiEndpoint?: string;
    pageCategory?: string;
    parentSocialId?: string;
    placeId?: string;
    hasTransitionedToNewPageExperience?: boolean;
    username?: string;
    drnId?: string;
    connectableStatus: ConnectableStatus;
}

export interface SocialIdsPagination {
    total: number;
    count: number;
    limit: number;
    currentPage: number;
    totalPages: number;
    nextPage: string;
    previousPage: string;
}
