@if (title()) {
    <div class="flex justify-between">
        <div class="malou-text-10--regular malou-color-text-2 flex gap-1 py-1 {{ titleClass() }}">
            <span>{{ title() }}</span>
            @if (required()) {
                <span>*</span>
            }
        </div>
    </div>
}
<div
    class="h-full w-full rounded-[5px] border border-malou-color-background-dark bg-white"
    [ngClass]="{ 'border-dotted border-malou-color-primary': isDragging(), 'p-3': !uploadViaImagePreview() }">
    @if (isDragging()) {
        <ng-container [ngTemplateOutlet]="dragOverTemplate"></ng-container>
    } @else if (job()) {
        <app-loader-progress [progress]="uploadMediaProgress"></app-loader-progress>
    } @else if (isProcessingMedia()) {
        <ng-container [ngTemplateOutlet]="processingMediaTemplate"></ng-container>
    } @else if (uploadViaImagePreview() && !media()) {
        <ng-container [ngTemplateOutlet]="addMediaViaImagePreviewTemplate"></ng-container>
    } @else if (!media()) {
        <ng-container [ngTemplateOutlet]="addMediaTemplate"></ng-container>
    } @else {
        <div class="flex h-full w-full flex-col items-center gap-x-1.5">
            <div class="min-w-120px">
                <app-media-thumbnail-list
                    [medias]="mediasForThumbnailList()"
                    [showEditMediaButton]="false"
                    [showRemoveMediaButton]="false"
                    [showOnReUploadMediaButton]="true"
                    [imageViewerWrapperCss]="imageViewerWrapperCss()"
                    [isReadonly]="false"
                    (reUploadMedia)="onReuploadMedia()"></app-media-thumbnail-list>
            </div>
            <span [matMenuTriggerFor]="uploadMenu" #menuTrigger="matMenuTrigger"></span>
        </div>
    }
</div>

<ng-template #processingMediaTemplate>
    @if (uploadViaImagePreview()) {
        <div
            class="flex h-full w-full items-center gap-x-5 rounded-md border border-dotted border-malou-color-background-dark bg-white p-4">
            <mat-spinner diameter="30"></mat-spinner>
        </div>
    } @else {
        <div class="flex h-[75px] items-center gap-x-5 rounded-md border border-dotted border-malou-color-background-dark bg-white p-4">
            <mat-spinner diameter="30"></mat-spinner>
            <div class="flex flex-col gap-y-1">
                <div class="malou-text-11--semibold text-malou-color-text-1">
                    {{ 'image_uploader.processing_image' | translate }}
                </div>
            </div>
        </div>
    }
</ng-template>

<ng-template #addMediaViaImagePreviewTemplate>
    <div
        class="group relative flex h-full w-full items-center gap-x-5 rounded-md border border-dotted border-malou-color-background-dark bg-white p-4">
        <mat-icon class="!h-fit !w-fit !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
        <div
            class="invisible absolute left-0 top-0 flex h-full w-full items-center justify-center rounded-md border border-malou-color-text-1 bg-malou-color-text-1/40 p-1 leading-[0] group-hover:visible"
            [matMenuTriggerFor]="uploadMenu">
            <mat-icon class="h-[16px] !w-[16px]" color="white" [svgIcon]="SvgIcon.UPLOAD"></mat-icon>
        </div>
    </div>
</ng-template>

<ng-template #addMediaTemplate>
    <div class="flex items-center gap-x-5 rounded-md border border-dotted border-malou-color-background-dark bg-white p-4">
        @if (!media()) {
            <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
        }
        <div class="flex flex-col gap-y-1">
            <div class="malou-text-11--semibold text-malou-color-text-1">
                <span class="cursor-pointer text-malou-color-primary" [matMenuTriggerFor]="uploadMenu">{{
                    'image_uploader.add_media' | translate
                }}</span>
                {{ 'image_uploader.or_drag_and_drop' | translate }}
            </div>
            <div class="malou-text-10--regular italic text-malou-color-text-2">
                {{ 'image_uploader.max_size' | translate: { size: maxMediaSizeInMo() } }}, {{ mediaFormatAccepted() | join: ', ' }}.
            </div>
        </div>
    </div>
</ng-template>

<ng-template #dragOverTemplate>
    @if (uploadViaImagePreview()) {
        <div class="flex h-full w-full items-center gap-x-5 rounded-md bg-white p-4">
            <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
        </div>
    } @else {
        <div class="flex h-[75px] items-center gap-x-5 rounded-md bg-white p-4">
            <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
            <div class="malou-text-11--semibold text-malou-color-text-1">
                {{ 'image_uploader.drag_and_drop_here' | translate }}
            </div>
        </div>
    }
</ng-template>

<input
    type="file"
    hidden
    [multiple]="false"
    [accept]="acceptedMimeTypes()"
    [disabled]="disabled()"
    (change)="onImportFromFile($event)"
    #fileInput />

<mat-menu class="malou-mat-menu malou-box-shadow rounded-md" #uploadMenu="matMenu">
    <button mat-menu-item [disabled]="disabled()" (click)="fileInput.click()">
        <mat-icon class="!h-[16px] !w-[16px]" color="primary" [svgIcon]="SvgIcon.ADD"></mat-icon>
        <span class="malou-text-12--regular text-malou-color-text-2">
            {{ 'common.upload_from_computer' | translate }}
        </span>
    </button>
    <button mat-menu-item [disabled]="disabled()" (click)="onImportMediaFromGallery()">
        <mat-icon class="!h-[16px] !w-[16px]" color="primary" [svgIcon]="SvgIcon.IMAGE"></mat-icon>
        <span class="malou-text-12--regular text-malou-color-text-2">{{ 'common.upload_from_gallery' | translate }}</span>
    </button>
</mat-menu>
