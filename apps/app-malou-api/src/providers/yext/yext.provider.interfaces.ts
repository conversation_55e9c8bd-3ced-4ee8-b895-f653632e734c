import { LanguageCodeISO_1, YextAddRequestStatus, YextPublisherId } from '@malou-io/package-utils';

// Different from EntityType, I know... These ones should be lowercase and EntityType is UPPER_CASE. Used only for add request creation and entity creation
export enum YextCreationEntityType {
    LOCATION = 'location',
    RESTAURANT = 'restaurant',
    HOTEL = 'hotel',
    HEALTHCARE_FACILITY = 'healthcare_facility',
    HEALTHCARE_PROFESSIONAL = 'healthcare_professional',
    ATM = 'atm',
}

export enum YextEntityType {
    RESTAURANT = 'RESTAURANT',
    LOCATION = 'LOCATION',
    HOTEL = 'HOTEL',
    HEALTHCARE_FACILITY = 'HEALTHCARE_FACILITY',
    HEALTHCARE_PROFESSIONAL = 'HEALTHCARE_PROFESSIONAL',
    ATM = 'ATM',
}

export enum YextListingStatus {
    WAITING_ON_YEXT = 'WAITING_ON_YEXT',
    WAITING_ON_CUSTOMER = 'WAITING_ON_CUSTOMER',
    WAITING_ON_PUBLISHER = 'WAITING_ON_PUBLISHER',
    LIVE = 'LIVE',
    UNAVAILABLE = 'UNAVAILABLE',
    OPTED_OUT = 'OPTED_OUT',
}

enum YextFeature {
    DUAL_SYNC = 'DUAL_SYNC',
    SUPPRESSION = 'SUPPRESSION',
    SUPPRESSION_BY_URL = 'SUPPRESSION_BY_URL',
    PUBLISHER_SUGGESTIONS = 'PUBLISHER_SUGGESTIONS',
    REVIEW_MONITORING = 'REVIEW_MONITORING',
    ANALYTICS = 'ANALYTICS',
}

export enum UnsupportedYextPublisherId {
    FACEBOOK = 'FACEBOOK',
    GMB = 'GOOGLEMYBUSINESS',
    MAPSTR = 'MAPSTR',

    // To check
    ALL_MENUS = 'ALLMENUS', // No info in GSheet
    NATIONALHEALTHSERVICE = 'NATIONALHEALTHSERVICE',

    // TO CHECK, but CA only
    // Restaurant Loam, 400 Rue Notre-Dame Est Montréal, QC H2Y 1C8
    MENUPIX = 'MENUPIX',
    OPENDICA = 'OPENDICA',
    OURBISCA = 'OURBISCA',
    PROFILECANADA = 'PROFILECANADA',
    YPCA = 'YPCA',
}

export type YextEntityDayHours =
    | {
          isClosed: true;
      }
    | {
          isClosed: false;
          openIntervals: YextEntityOpenInterval[];
      };

export interface YextEntityOpenInterval {
    // must be hh:mm
    start: string;
    // must be hh:mm
    end: string;
}

export interface YextEntityHolidayHours {
    // must be YYYY-MM-DD
    date: string;
    isClosed?: boolean;
    openIntervals?: YextEntityOpenInterval[];
}

export interface YextEntityHours {
    monday?: YextEntityDayHours;
    tuesday?: YextEntityDayHours;
    wednesday?: YextEntityDayHours;
    thursday?: YextEntityDayHours;
    friday?: YextEntityDayHours;
    saturday?: YextEntityDayHours;
    sunday?: YextEntityDayHours;
    holidayHours?: YextEntityHolidayHours[];
}

interface YextEntityAddress {
    city: string;
    countryCode: string;
    extraDescription?: string;
    line1: string;
    line2?: string;
    postalCode?: string;
    region?: string; // required if countryCode is US
    sublocality?: string;
}

interface YextEntityRequestEntityMeta {
    // don't know why but we need this and it must match the address.countryCode
    countryCode: string;
    language: LanguageCodeISO_1;
    entityType: YextEntityType;
}

export interface YextEntity<Meta = YextEntityRequestEntityMeta> {
    meta: Meta;
    name: string;
    address: YextEntityAddress;
    categoryIds?: string[]; // DEPRECATED
    categories?: {
        googleBusinessProfile: string[];
    };
    closed?: boolean; // todo do this mean definitively closed or temporary ?
    // deliveryHours?: null; // todo Do we need this or yext will take 'hours' by default ?
    description?: string;
    appleBusinessDescription?: string; // business description to be sent to Apple, max 500 characters
    displayCoordinate?: {
        latitude: number;
        longitude: number;
    };
    hours?: YextEntityHours; // todo maybe also use yext happy hours or yext brunch hours, but it's maybe only for google and we do not use google with yext
    locationType?: YextEntityType; // todo only restaurant or other types ?
    logo?: {
        image: {
            url: string;
        };
    };
    mainPhone?: string;
    menuUrl?: {
        url: string;
    };
    orderUrl?: {
        url: string;
    };
    reservationUrl?: {
        url: string;
    };
    websiteUrl?: {
        url: string;
    };
    facebookPageUrl?: string;
    linkedInUrl?: string;
    tikTokUrl?: string;
    pinterestUrl?: string;
    youTubeChannelUrl?: string;
    acceptsReservations?: boolean;
}

export interface YextUpdateEntityResponseBody {
    meta: {
        uuid: string;
    };
    response: YextEntity<YextUpdateEntityResponseBodyMeta>;
}

interface YextUpdateEntityResponseBodyMeta {
    accountId: string;
    countryCode: string;
    createdTimestamp: string;
    entityType: string;
    folderId: string;
    id: string;
    labels: string[];
    language: string;
    timestamp: string;
    uid: string;
}

export interface YextListing {
    id: string;
    locationId: string;
    accountId: string;
    publisherId: YextPublisherId | UnsupportedYextPublisherId;
    status: YextListingStatus;
    additionalStatus: YextListingAdditionalStatus;
    listingUrl?: string;
    loginUrl: string;
    screenshotUrl: string;
    statusDetails: [
        {
            code: string;
            type: ListingStatusDetailType;
            message: string;
            actionable: true;
            unavailableReasonType: string;
        },
    ];
    alternateBrands: [
        {
            brandName: string;
            listingUrl: string;
        },
    ];
}

enum YextListingAdditionalStatus {
    CONNECTED = 'CONNECTED',
    NOT_CONNECTED = 'NOT_CONNECTED',
}

enum ListingStatusDetailType {
    UNAVAILABLE_REASON = 'UNAVAILABLE_REASON',
    WARNING = 'WARNING',
}

export interface YextGetListingsResponseBody {
    meta: {
        uuid: string;
    };
    response: {
        count: number;
        listings: YextListing[];
        pageToken: string;
    };
}

export interface YextPublisher {
    id: YextPublisherId | UnsupportedYextPublisherId;
    name: string;
    url: string;
    alternateBrands: {
        name: string;
        url: string;
    }[];
    supportedCountries: string[];
    supportedLocationTypes: YextEntityType[];
    supportedEntityTypes: YextEntityType[];
    features: YextFeature[];
    typicalUpdateSpeed: string;
}

export enum YextServiceStatus {
    ACTIVE = 'ACTIVE',
    STOPPED = 'STOPPED',
}

export interface YextCancelServiceRequestBody {
    locationId: string;
    locationAccountId: string;
    skus?: string[]; // leave blank to cancel all services
}

export interface YextCancelServiceResponseBody {
    response: { sku: string; status: YextServiceStatus }[];
}

export type YextService = {
    id: number;
    sku: string;
    serviceDescription: string;
    agreementId: number;
    locationId: string;
    status: YextServiceStatus;
    started: string;
};

export interface YextGetServicesRequestBody {
    locationId: string;
    locationAccountId: string;
}

export interface YextGetServicesResponseBody {
    response: { count: number; services: YextService[] };
}

export interface YextCreateAddRequestRequestBody {
    newLocationId: string;
    newLocationAccountId?: string;
    // Needed if an account is created, eg: the newLocationAccountId does not exist
    // default is the location name
    newLocationAccountName?: string;
    // default: 'location'
    newEntityType?: YextCreationEntityType;
    newEntityData: YextEntity;
    skus: string[];
}

export interface YextCreateAddRequestResponseBody {
    meta: {
        uuid: string;
    };
    response: {
        id: string;
        status: YextAddRequestStatus;
        newLocationAccountId: string;
    };
}

export interface YextGetAddRequestResponseBody {
    meta: {
        uuid: string;
    };
    response: {
        id: string;
        status: YextAddRequestStatus;
    };
}
