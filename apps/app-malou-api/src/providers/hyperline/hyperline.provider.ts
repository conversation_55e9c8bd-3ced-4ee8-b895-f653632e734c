import axios, { AxiosInstance } from 'axios';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { ProviderMetricsService } from ':providers/provider.metrics.service';

import { IHyperlineProvider } from './hyperline.provider.interface';
import {
    GetLocationsByOrganizationRequest,
    GetLocationsByOrganizationResponse,
    HyperlineApiCustomer,
    HyperlineApiOrganization,
    HyperlineLocation,
} from './hyperline.provider.interfaces';

@singleton()
export class HyperlineProvider implements IHyperlineProvider {
    private _axiosInstance: AxiosInstance;

    constructor(private readonly _providerMetricsService: ProviderMetricsService) {
        this._axiosInstance = axios.create({
            baseURL: Config.vendors.hyperline.baseUrl,
            headers: {
                Authorization: `Bearer ${Config.vendors.hyperline.apiKey}`,
                'Content-Type': 'application/json',
            },
        });
    }

    async getLocationsByOrganization(request: GetLocationsByOrganizationRequest): Promise<GetLocationsByOrganizationResponse> {
        try {
            const parentOrganization = await this._getOrganization(request.organizationProviderId);
            const legalEntities = await Promise.all(
                parentOrganization.child_customer_ids.map((customerId) => this._getOrganization(customerId))
            );
            const locationIds = legalEntities.flatMap((legalEntity) => legalEntity.child_customer_ids);

            const locations: HyperlineLocation[] = [];

            if (locationIds?.length > 0) {
                const locationPromises = locationIds.map((locationId) => this._getCustomer(locationId));
                const locationsData = await Promise.all(locationPromises);
                for (const location of locationsData) {
                    locations.push({
                        id: location.id,
                        name: location.name,
                        placeId: location.custom_properties.google_place_id,
                        malouRestaurantId: location.external_id,
                        isBrandAccount: !!location.custom_properties.brand_account,
                    });
                }
            }

            return {
                locations,
            };
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching locations by organization', {
                organizationProviderId: request.organizationProviderId,
                error: error.message,
            });
            throw error;
        }
    }

    private async _getCustomer(customerId: string): Promise<HyperlineApiCustomer> {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'hyperline',
            requestId: 'hyperline.api.customer.get',
            request: async () => this._axiosInstance.get<HyperlineApiCustomer>(`/customers/${customerId}`),
        });
        return data;
    }

    private async _getOrganization(customerId: string): Promise<HyperlineApiOrganization> {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'hyperline',
            requestId: 'hyperline.api.organization.get',
            request: async () => this._axiosInstance.get<HyperlineApiOrganization>(`/organisations/${customerId}`),
        });
        return data;
    }
}
