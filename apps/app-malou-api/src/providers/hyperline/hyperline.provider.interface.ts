import {
    GetLocationsByOrganizationRequest,
    GetLocationsByOrganizationResponse,
    UpdateCustomerRequest,
    UpdateCustomerResponse,
} from './hyperline.provider.interfaces';

export interface IHyperlineProvider {
    /**
     * Get all locations for an organization by its providerId
     */
    getLocationsByOrganization(request: GetLocationsByOrganizationRequest): Promise<GetLocationsByOrganizationResponse>;

    /**
     * Update a customer's external_id field
     */
    updateCustomer(request: UpdateCustomerRequest): Promise<UpdateCustomerResponse>;
}
