import { Router } from 'express';
import { singleton } from 'tsyringe';

import { Role } from '@malou-io/package-utils';

import { apiKeyAuthorize } from ':modules/api-keys/middlewares';
import StoreLocatorController from ':modules/store-locator/store-locator.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class StoreLocatorRouter {
    constructor(private _storeLocatorController: StoreLocatorController) {}

    init(router: Router): void {
        // Public API routes
        router.get('/store-locator/:organizationId/stores', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleGetStores(req, res, next)
        );

        router.get('/store-locator/:organizationId/configuration', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleGetOrganizationConfiguration(req, res, next)
        );

        // App API routes
        router.get('/store-locator/:organizationId/all-stores', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleGetStores(req, res, next)
        );

        router.get('/store-locator/:organizationId/style-configuration', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleGetOrganizationStyleConfiguration(req, res, next)
        );

        router.get('/store-locator/:organizationId/organization-configuration', authorize([Role.ADMIN]), (req: any, res, next) =>
            this._storeLocatorController.handleGetStoreLocatorOrganizationConfiguration(req, res, next)
        );

        router.put(
            '/store-locator/:organizationId/organization-configuration/ai-settings',
            authorize([Role.ADMIN]),
            (req: any, res, next) => this._storeLocatorController.handleUpdateOrganizationConfigurationAiSettings(req, res, next)
        );
    }
}
