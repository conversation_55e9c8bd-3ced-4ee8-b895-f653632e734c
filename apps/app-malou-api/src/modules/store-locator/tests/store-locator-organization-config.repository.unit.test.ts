import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformKey, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultAttribute, getDefaultRestaurantAttribute } from ':modules/attributes/tests/attribute.builder';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';
import { getDefaultStoreLocatorOrganizationConfig } from ':modules/store-locator/tests/store-locator-organization-config.builder';
import { OrganizationConfigurationAiSettingsUpdate } from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';

describe('StoreLocatorOrganizationConfigRepository', () => {
    beforeAll(() => {
        registerRepositories([
            'AttributesRepository',
            'RestaurantAttributesRepository',
            'RestaurantKeywordsRepository',
            'KeywordsTempRepository',
            'StoreLocatorOrganizationConfigRepository',
        ]);
    });

    describe('updateAiSettings', () => {
        const repository = container.resolve(StoreLocatorOrganizationConfigRepository);

        it('should update AI settings successfully', async () => {
            const organizationId = newDbId();
            const restaurantId = newDbId();
            const newRestaurantAttributeId = newDbId();

            const testCase = new TestCaseBuilderV2<
                'attributes' | 'restaurantAttributes' | 'keywordsTemp' | 'restaurantKeywords' | 'storeLocatorOrganizationConfigs'
            >({
                seeds: {
                    attributes: {
                        data() {
                            return [
                                getDefaultAttribute()
                                    .attributeId('attribute_id_1')
                                    .attributeName({ fr: 'Attribute 1' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                                getDefaultAttribute()
                                    .attributeId('attribute_id_2')
                                    .attributeName({ fr: 'Attribute 2' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),

                                getDefaultAttribute()
                                    .attributeId('new_attribute_id')
                                    .attributeName({
                                        fr: 'New Restaurant Attribute',
                                    })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                            ];
                        },
                    },
                    restaurantAttributes: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantAttribute()
                                    .attributeId(dependencies.attributes()[0]._id)
                                    .restaurantId(restaurantId)
                                    .build(),
                                getDefaultRestaurantAttribute()
                                    .attributeId(dependencies.attributes()[1]._id)
                                    .restaurantId(restaurantId)
                                    .build(),
                                // New restaurant attribute
                                getDefaultRestaurantAttribute()
                                    ._id(newRestaurantAttributeId)
                                    .attributeId(dependencies.attributes()[2]._id)
                                    .restaurantId(restaurantId)
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('keyword text 1').build(),
                                getDefaultKeywordTemp().text('keyword text 2').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            const restaurantAttributeIds = [
                                dependencies.restaurantAttributes()[0]._id,
                                dependencies.restaurantAttributes()[1]._id,
                            ];
                            const restaurantKeywordIds = dependencies.restaurantKeywords().map((rk) => rk._id);
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(organizationId)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        restaurantAttributeIds,
                                        restaurantKeywordIds,
                                        specialAttributes: [],
                                    })
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoreLocatorOrganizationConfiguration['aiSettings'] {
                    const attributes = dependencies.attributes;
                    const restaurantAttributes = dependencies.restaurantAttributes;
                    const restaurantKeywords = dependencies.restaurantKeywords;
                    const keywordsTemp = dependencies.keywordsTemp;
                    return {
                        tone: ['friendly', 'professional'],
                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                        restaurantAttributeIds: restaurantAttributes.map((category) => category._id.toString()),
                        restaurantKeywordIds: restaurantKeywords.map((rk) => rk._id.toString()),
                        specialAttributes: [
                            {
                                restaurantId: restaurantId.toString(),
                                text: 'Special dietary options available',
                            },
                        ],
                        attributes: restaurantAttributes.map((restaurantAttribute) => {
                            const attribute = attributes.find((a) => a._id.toString() === restaurantAttribute.attributeId.toString());
                            return {
                                restaurantAttributeId: restaurantAttribute._id.toString(),
                                dbAttributeId: attribute!._id.toString(),
                                restaurantId: restaurantAttribute.restaurantId.toString(),
                                attributeId: attribute!.attributeId ?? '',
                                platformKey: attribute!.platformKey ?? PlatformKey.GMB,
                                attributeName: {
                                    fr: attribute?.attributeName.fr ?? '',
                                    en: attribute?.attributeName.en ?? undefined,
                                    es: attribute?.attributeName.es ?? undefined,
                                    it: attribute?.attributeName.it ?? undefined,
                                },
                            };
                        }),
                        keywords: restaurantKeywords.map((restaurantKeyword) => ({
                            restaurantKeywordId: restaurantKeyword._id.toString(),
                            text: keywordsTemp.find((kt) => kt._id.toString() === restaurantKeyword.keywordId.toString())!.text,
                            restaurantId: restaurantKeyword.restaurantId.toString(),
                            keywordId: restaurantKeyword.keywordId.toString(),
                        })),
                    };
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();
            const seededObjects = testCase.getSeededObjects();
            const restaurantAttributes = seededObjects.restaurantAttributes;
            const restaurantKeywordIds = seededObjects.restaurantKeywords.map((rk) => rk._id);

            const newAiSettings: OrganizationConfigurationAiSettingsUpdate = {
                tone: ['friendly', 'professional'],
                languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                restaurantAttributeIds: [
                    restaurantAttributes[0]._id.toString(),
                    restaurantAttributes[1]._id.toString(),
                    newRestaurantAttributeId.toString(),
                ],
                restaurantKeywordIds: restaurantKeywordIds.map((id) => id.toString()),
                specialAttributes: [
                    {
                        restaurantId: restaurantId.toString(),
                        text: 'Special dietary options available',
                    },
                ],
            };

            const result = await repository.updateAiSettings(organizationId.toString(), newAiSettings);
            expect(result.aiSettings).toEqual(expectedResult);
        });
    });
});
