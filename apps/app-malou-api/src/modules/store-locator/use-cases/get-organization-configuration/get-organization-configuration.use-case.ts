import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { GetStoreLocatorOrganizationConfigurationDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';

import { GenerateTailwindConfigurationService } from ':modules/store-locator/services/generate-tailwind-configuration/generate-tailwind-configuration.service';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class GetOrganizationConfigurationUseCase {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateTailwindConfigurationService: GenerateTailwindConfigurationService,
        private readonly _cloudStorageService: AwsS3
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorOrganizationConfigurationDto> {
        const storeLocatorOrganizationConfig = await this._storeLocatorOrganizationConfigRepository.findOne({
            filter: { organizationId: toDbId(organizationId) },
            options: { lean: true, populate: [{ path: 'organization' }] },
        });
        assert(storeLocatorOrganizationConfig?.organization);

        const { config: tailwindConfig, classesMap: tailwindClassesMap } = this._generateTailwindConfigurationService.execute({
            storeLocatorOrganizationConfig,
        });
        const favIconUrl = `${this._cloudStorageService.getBucketBaseUrl()}/store-locator/organization/${organizationId}/favicons/favicon.png`;

        return {
            cloudfrontDistributionId: storeLocatorOrganizationConfig.cloudfrontDistributionId,
            organizationName: storeLocatorOrganizationConfig.organization.name,
            baseUrl: storeLocatorOrganizationConfig.baseUrl,
            tailwindConfig,
            tailwindClassesMap,
            favIconUrl,
        };
    }
}
