import { container } from 'tsyringe';

import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { PlatformKey, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultAttribute, getDefaultRestaurantAttribute } from ':modules/attributes/tests/attribute.builder';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultStoreLocatorOrganizationConfig } from ':modules/store-locator/tests/store-locator-organization-config.builder';
import { GetStoreLocatorOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-store-locator-organization-configuration/get-store-locator-organization-configuration.use-case';

describe('GetStoreLocatorOrganizationConfigurationUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'AttributesRepository',
            'RestaurantAttributesRepository',
            'RestaurantKeywordsRepository',
            'KeywordsTempRepository',
            'StoreLocatorOrganizationConfigRepository',
        ]);
    });

    describe('execute', () => {
        it('should return store locator organization configuration response DTO', async () => {
            const useCase = container.resolve(GetStoreLocatorOrganizationConfigurationUseCase);
            const organizationId = newDbId();
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<
                'attributes' | 'restaurantAttributes' | 'keywordsTemp' | 'restaurantKeywords' | 'storeLocatorOrganizationConfigs'
            >({
                seeds: {
                    attributes: {
                        data() {
                            return [
                                getDefaultAttribute()
                                    .attributeId('attribute_id_1')
                                    .attributeName({ fr: 'Attribute 1' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                                getDefaultAttribute()
                                    .attributeId('attribute_id_2')
                                    .attributeName({ fr: 'Attribute 2' })
                                    .platformKey(PlatformKey.GMB)
                                    .build(),
                            ];
                        },
                    },
                    restaurantAttributes: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantAttribute()
                                    .attributeId(dependencies.attributes()[0]._id)
                                    .restaurantId(restaurantId)
                                    .build(),
                                getDefaultRestaurantAttribute()
                                    .attributeId(dependencies.attributes()[1]._id)
                                    .restaurantId(restaurantId)
                                    .build(),
                            ];
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('keyword text 1').build(),
                                getDefaultKeywordTemp().text('keyword text 2').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(restaurantId)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                    storeLocatorOrganizationConfigs: {
                        data(dependencies) {
                            const restaurantAttributeIds = dependencies.restaurantAttributes().map((ra) => ra._id);
                            const restaurantKeywordIds = dependencies.restaurantKeywords().map((rk) => rk._id);
                            return [
                                getDefaultStoreLocatorOrganizationConfig()
                                    .organizationId(organizationId)
                                    .aiSettings({
                                        tone: ['inspiring', 'friendly'],
                                        languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                                        restaurantAttributeIds,
                                        restaurantKeywordIds,
                                        specialAttributes: [],
                                    })
                                    .build(),
                                getDefaultStoreLocatorOrganizationConfig().organizationId(newDbId()).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoreLocatorOrganizationConfigurationResponseDto {
                    const restaurantAttributes = dependencies.restaurantAttributes;
                    const attributes = dependencies.attributes;
                    const restaurantKeywords = dependencies.restaurantKeywords;
                    const keywordsTemp = dependencies.keywordsTemp;
                    const config = dependencies.storeLocatorOrganizationConfigs[0];

                    return {
                        id: config._id.toString(),
                        organizationId: config.organizationId.toString(),
                        cloudfrontDistributionId: config.cloudfrontDistributionId,
                        baseUrl: config.baseUrl,
                        isLive: config.isLive,
                        styles: config.styles,
                        plugins: config.plugins,
                        aiSettings: {
                            tone: config.aiSettings.tone,
                            languageStyle: config.aiSettings.languageStyle,
                            restaurantAttributeIds: config.aiSettings.restaurantAttributeIds.map((id) => id.toString()),
                            restaurantKeywordIds: config.aiSettings.restaurantKeywordIds.map((id) => id.toString()),
                            specialAttributes: config.aiSettings.specialAttributes.map((attr) => ({
                                restaurantId: attr.restaurantId.toString(),
                                text: attr.text,
                            })),
                            attributes: restaurantAttributes.map((restaurantAttribute) => {
                                const attribute = attributes.find((a) => a._id.toString() === restaurantAttribute.attributeId.toString());
                                return {
                                    restaurantAttributeId: restaurantAttribute._id.toString(),
                                    dbAttributeId: attribute!._id.toString(),
                                    restaurantId: restaurantAttribute.restaurantId.toString(),
                                    attributeId: attribute!.attributeId,
                                    platformKey: attribute!.platformKey,
                                    attributeName: {
                                        fr: attribute?.attributeName.fr ?? '',
                                        en: attribute?.attributeName.en ?? undefined,
                                        es: attribute?.attributeName.es ?? undefined,
                                        it: attribute?.attributeName.it ?? undefined,
                                    },
                                };
                            }),
                            keywords: restaurantKeywords.map((restaurantKeyword) => ({
                                restaurantKeywordId: restaurantKeyword._id.toString(),
                                text: keywordsTemp.find((kt) => kt._id.toString() === restaurantKeyword.keywordId.toString())?.text || '',
                                restaurantId: restaurantKeyword.restaurantId.toString(),
                                keywordId: restaurantKeyword.keywordId.toString(),
                            })),
                        },
                    };
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();
            const result = await useCase.execute(organizationId.toString());

            expect(result).toEqual(expectedResult);
        });

        it('should throw an assertion error when organization configuration is not found', async () => {
            const useCase = container.resolve(GetStoreLocatorOrganizationConfigurationUseCase);
            const nonExistentOrganizationId = newDbId();

            const testCase = new TestCaseBuilderV2<'storeLocatorOrganizationConfigs'>({
                seeds: {
                    storeLocatorOrganizationConfigs: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult() {
                    return null;
                },
            });
            await testCase.build();

            await expect(useCase.execute(nonExistentOrganizationId.toString())).rejects.toThrow(
                `Store locator organization configuration not found for organization ID: ${nonExistentOrganizationId}`
            );
        });
    });
});
