import { singleton } from 'tsyringe';

import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';

import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class GetStoreLocatorOrganizationConfigurationUseCase {
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute(organizationId: string): Promise<StoreLocatorOrganizationConfigurationResponseDto> {
        const entity = await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        return entity.toDto();
    }
}
