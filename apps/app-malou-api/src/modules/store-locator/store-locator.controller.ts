import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    GetOrganizationConfigurationParamsDto,
    getOrganizationConfigurationParamsValidator,
    GetStoreLocatorOrganizationConfigurationDto,
    GetStoreLocatorOrganizationStyleConfigurationDto,
    GetStoreLocatorStorePageDto,
    GetStoreLocatorStoresParamsDto,
    getStoreLocatorStoresParamsValidator,
    StoreLocatorOrganizationConfigurationResponseDto,
    UpdateOrganizationConfigurationAiSettingsBodyDto,
    updateOrganizationConfigurationAiSettingsBodyValidator,
    UpdateOrganizationConfigurationAiSettingsParamsDto,
    updateOrganizationConfigurationAiSettingsParamsValidator,
} from '@malou-io/package-dto';
import { ApiResultError, ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { GetOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-organization-configuration/get-organization-configuration.use-case';
import { GetStoreLocatorOrganizationStyleConfigurationUseCase } from ':modules/store-locator/use-cases/get-organization-style-configuration/get-organization-style-configuration.use-case';
import { GetStoreLocatorOrganizationConfigurationUseCase } from ':modules/store-locator/use-cases/get-store-locator-organization-configuration/get-store-locator-organization-configuration.use-case';
import { GetStoreLocatorStoresUseCase } from ':modules/store-locator/use-cases/get-store-locator-stores/get-store-locator-stores.use-case';
import UpdateOrganizationConfigAISettingsUseCase from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';

@singleton()
export default class StoreLocatorController {
    constructor(
        private readonly _getStoreLocatorStoresUseCase: GetStoreLocatorStoresUseCase,
        private readonly _getOrganizationConfigurationUseCase: GetOrganizationConfigurationUseCase,
        private readonly _getStoreLocatorOrganizationStyleConfigurationUseCase: GetStoreLocatorOrganizationStyleConfigurationUseCase,
        private readonly _updateOrganizationConfigAISettingsUseCase: UpdateOrganizationConfigAISettingsUseCase,
        private readonly _getStoreLocatorOrganizationConfigurationUseCase: GetStoreLocatorOrganizationConfigurationUseCase
    ) {}

    @Params(getStoreLocatorStoresParamsValidator)
    async handleGetStores(
        req: Request<GetStoreLocatorStoresParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorStorePageDto[], ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorStoresUseCase.execute(organizationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationConfigurationDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getOrganizationConfigurationUseCase.execute(organizationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetStoreLocatorOrganizationConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorOrganizationConfigurationUseCase.execute(organizationId);

            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationConfigurationParamsValidator)
    async handleGetOrganizationStyleConfiguration(
        req: Request<GetOrganizationConfigurationParamsDto, never, never>,
        res: Response<ApiResultV2<GetStoreLocatorOrganizationStyleConfigurationDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const data = await this._getStoreLocatorOrganizationStyleConfigurationUseCase.execute(organizationId);
            return res.json({ data });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateOrganizationConfigurationAiSettingsParamsValidator)
    @Body(updateOrganizationConfigurationAiSettingsBodyValidator)
    async handleUpdateOrganizationConfigurationAiSettings(
        req: Request<UpdateOrganizationConfigurationAiSettingsParamsDto, never, UpdateOrganizationConfigurationAiSettingsBodyDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const { aiSettings } = req.body;
            const organizationConfig = await this._updateOrganizationConfigAISettingsUseCase.execute({ organizationId, aiSettings });
            return res.json({ data: organizationConfig });
        } catch (err) {
            next(err);
        }
    }
}
