import { singleton } from 'tsyringe';

import {
    EntityRepository,
    IStoreLocatorOrganizationConfig,
    IStoreLocatorOrganizationConfigWithKeywordsAndAttributes,
    StoreLocatorOrganizationConfigModel,
    toDbId,
} from '@malou-io/package-models';
import { MalouErrorCode, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { OrganizationConfigurationAiSettingsUpdate } from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';

import { StoreLocatorOrganizationConfiguration } from './entities/store-locator.organization-configuration.entity';

const DEFAULT_POPULATE_OPTIONS = [
    {
        path: 'aiSettings' as const,
        populate: [
            {
                path: 'restaurantAttributes' as const,
                populate: {
                    path: 'attribute' as const,
                    options: { projection: { _id: 1, attributeId: 1, attributeName: 1, platformKey: 1 }, lean: true },
                },
                options: { lean: true },
            },
            {
                path: 'restaurantKeywords' as const,
                populate: [{ path: 'keyword' as const, options: { projection: { _id: 1, keywordId: 1, text: 1 }, lean: true } }],
                options: { projection: { _id: 1, keywordId: 1, keyword: 1, restaurantId: 1 }, lean: true },
            },
        ] as any,
    },
];

@singleton()
export default class StoreLocatorOrganizationConfigRepository extends EntityRepository<IStoreLocatorOrganizationConfig> {
    constructor() {
        super(StoreLocatorOrganizationConfigModel);
    }

    async getOrganizationConfiguration(organizationId: string): Promise<StoreLocatorOrganizationConfiguration> {
        const document = await this.findOne({
            filter: { organizationId: toDbId(organizationId) },
            options: { populate: DEFAULT_POPULATE_OPTIONS, lean: true },
        });

        if (!document) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: `Store locator organization configuration not found for organization ID: ${organizationId}`,
                metadata: { organizationId },
            });
        }

        return this.toEntity(document as IStoreLocatorOrganizationConfigWithKeywordsAndAttributes);
    }

    async updateAiSettings(
        organizationId: string,
        aiSettings: OrganizationConfigurationAiSettingsUpdate
    ): Promise<StoreLocatorOrganizationConfiguration> {
        const aiSettingsForDb = this._toAiSettingsDocument(aiSettings);

        const document = await this.findOneAndUpdate({
            filter: { organizationId: toDbId(organizationId) },
            update: { aiSettings: aiSettingsForDb },
            options: { populate: DEFAULT_POPULATE_OPTIONS, lean: true },
        });

        if (!document) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: `Store locator organization configuration not found for organization ID: ${organizationId}`,
                metadata: { organizationId },
            });
        }

        return this.toEntity(document as IStoreLocatorOrganizationConfigWithKeywordsAndAttributes);
    }

    toEntity(document: IStoreLocatorOrganizationConfigWithKeywordsAndAttributes): StoreLocatorOrganizationConfiguration {
        return new StoreLocatorOrganizationConfiguration({
            id: document._id.toString(),
            organizationId: document.organizationId.toString(),
            cloudfrontDistributionId: document.cloudfrontDistributionId,
            baseUrl: document.baseUrl,
            isLive: document.isLive,
            styles: document.styles,
            plugins: document.plugins,
            aiSettings: {
                tone: document.aiSettings.tone,
                languageStyle: document.aiSettings.languageStyle || StoreLocatorAiSettingsLanguageStyle.FORMAL,
                restaurantAttributeIds: document.aiSettings.restaurantAttributeIds?.map((id) => id.toString()),
                restaurantKeywordIds: document.aiSettings.restaurantKeywordIds?.map((id) => id.toString()),
                specialAttributes:
                    document.aiSettings.specialAttributes?.map((attr) => ({
                        restaurantId: attr.restaurantId.toString(),
                        text: attr.text,
                    })) || [],

                keywords: document.aiSettings.restaurantKeywords.map((restaurantKeyword) => ({
                    restaurantKeywordId: restaurantKeyword._id.toString(),
                    keywordId: restaurantKeyword.keywordId.toString(),
                    text: restaurantKeyword.keyword?.text,
                    restaurantId: restaurantKeyword.restaurantId.toString(),
                })),
                attributes: document.aiSettings.restaurantAttributes.map((restaurantAttribute) => ({
                    restaurantAttributeId: restaurantAttribute._id.toString(),
                    dbAttributeId: restaurantAttribute.attribute?._id.toString(),
                    restaurantId: restaurantAttribute.restaurantId.toString(),
                    attributeId: restaurantAttribute.attribute?.attributeId,
                    platformKey: restaurantAttribute.attribute?.platformKey,
                    attributeName: {
                        fr: restaurantAttribute.attribute?.attributeName.fr ?? '',
                        en: restaurantAttribute.attribute?.attributeName.en ?? undefined,
                        es: restaurantAttribute.attribute?.attributeName.es ?? undefined,
                        it: restaurantAttribute.attribute?.attributeName.it ?? undefined,
                    },
                })),
            },
        });
    }

    private _toAiSettingsDocument(aiSettings: OrganizationConfigurationAiSettingsUpdate): IStoreLocatorOrganizationConfig['aiSettings'] {
        return {
            tone: aiSettings.tone ?? [],
            languageStyle: aiSettings.languageStyle || StoreLocatorAiSettingsLanguageStyle.FORMAL,
            restaurantAttributeIds: aiSettings.restaurantAttributeIds?.map(toDbId) ?? [],
            restaurantKeywordIds: aiSettings.restaurantKeywordIds?.map(toDbId) ?? [],
            specialAttributes:
                aiSettings.specialAttributes?.map((attr) => ({
                    restaurantId: toDbId(attr.restaurantId),
                    text: attr.text,
                })) ?? [],
        };
    }
}
