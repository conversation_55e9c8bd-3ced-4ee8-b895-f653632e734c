import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';
import { EntityConstructor, PlatformKey, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

export type StoreLocatorOrganizationConfigurationProps = EntityConstructor<StoreLocatorOrganizationConfiguration> & {
    id: string;
};

export class StoreLocatorOrganizationConfiguration {
    id: string;
    organizationId: string;
    cloudfrontDistributionId: string;
    baseUrl: string;
    isLive: boolean;
    styles: {
        fonts: Array<{
            class: string;
            src: string;
            weight?: string;
            style?: string;
        }>;
        colors: Array<{
            class: string;
            value: string;
        }>;
        pages: {
            store: Record<string, unknown>;
        };
    };
    plugins?: {
        googleAnalytics?: {
            trackingId: string;
        };
    };
    aiSettings: {
        tone: string[];
        languageStyle: StoreLocatorAiSettingsLanguageStyle;
        restaurantAttributeIds: string[];
        restaurantKeywordIds: string[];
        specialAttributes: Array<{
            restaurantId: string;
            text: string;
        }>;
        attributes: Array<{
            restaurantAttributeId: string;
            dbAttributeId: string;
            restaurantId: string;
            attributeId: string;
            platformKey: PlatformKey;
            attributeName: {
                fr: string;
                en?: string;
                es?: string;
                it?: string;
            };
        }>;
        keywords: Array<{
            restaurantKeywordId: string;
            text: string;
            restaurantId: string;
            keywordId: string;
        }>;
    };

    constructor(props: StoreLocatorOrganizationConfigurationProps) {
        this.id = props.id;
        this.organizationId = props.organizationId;
        this.cloudfrontDistributionId = props.cloudfrontDistributionId;
        this.baseUrl = props.baseUrl;
        this.isLive = props.isLive;
        this.styles = props.styles;
        this.plugins = props.plugins;
        this.aiSettings = props.aiSettings;
    }

    toDto(): StoreLocatorOrganizationConfigurationResponseDto {
        return {
            id: this.id,
            organizationId: this.organizationId,
            cloudfrontDistributionId: this.cloudfrontDistributionId,
            baseUrl: this.baseUrl,
            isLive: this.isLive,
            styles: this.styles,
            plugins: this.plugins,
            aiSettings: this.aiSettings,
        };
    }
}
