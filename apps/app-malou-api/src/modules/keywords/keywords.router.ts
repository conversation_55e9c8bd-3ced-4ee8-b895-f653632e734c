import { Request, Router } from 'express';
import { singleton } from 'tsyringe';

import {
    AddRestaurantBricksBodyDto,
    AddRestaurantBricksParamsDto,
    CreateRestaurantKeywordBodyDto,
    CreateRestaurantKeywordParamsDto,
    GetKeywordsByRestaurantIdParamsDto,
    GetKeywordsForRestaurantRequestBodyDto,
    GetSelectedKeywordsCountByRestaurantIdParamsDto,
    GetStoreLocatorOrganizationKeywordsBodyDto,
    SelectRestaurantKeywordsBodyDto,
    SelectRestaurantKeywordsParamsDto,
    StartKeywordsGenerationParamsDto,
    StartKeywordsGenerationQueryDto,
    UpdateRestaurantBricksBodyDto,
    UpdateRestaurantBricksParamsDto,
} from '@malou-io/package-dto';
import { TimeInSeconds } from '@malou-io/package-utils';

import { casl } from ':helpers/casl/middlewares';
import { shouldCallMiddleware } from ':helpers/middleware';
import { RequestWithPermissions } from ':helpers/utils.types';
import { cacheMiddleware, CachePrefixKey } from ':plugins/cache-middleware';
import { authorize } from ':plugins/passport';
import { ExperimentationService } from ':services/experimentations-service/experimentation.service';

import KeywordsController from './keywords.controller';

@singleton()
export default class KeywordsRouter {
    constructor(
        private _keywordsController: KeywordsController,
        private readonly _experimentationService: ExperimentationService
    ) {}

    init(router: Router): void {
        router.put('/keywords/:keyword_id/volume-from-admin', authorize(), (req, res, next) =>
            this._keywordsController.handleUpdateKeywordVolumeFromAdmin(req, res, next)
        );

        router.get('/keywords/bricks', authorize(), (req, res, next) => this._keywordsController.handleGetBricksPaginated(req, res, next));

        router.get('/keywords/bricks/count', authorize(), (req, res, next) =>
            this._keywordsController.handleGetBricksCount(req, res, next)
        );

        router.get('/keywords/bricks/brick-generator/:key/:type', authorize(), (req, res, next) =>
            this._keywordsController.handleGetBricksGenerator(req, res, next)
        );

        router.post(
            '/keywords/bricks/restaurants/:restaurant_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions<UpdateRestaurantBricksParamsDto, any, UpdateRestaurantBricksBodyDto>, res, next) =>
                this._keywordsController.handleUpdateRestaurantBricks(req, res, next)
        );

        router.patch(
            '/keywords/bricks/restaurants/:restaurant_id',
            authorize(),
            casl(),
            (req: RequestWithPermissions<AddRestaurantBricksParamsDto, any, AddRestaurantBricksBodyDto>, res, next) =>
                this._keywordsController.handleAddRestaurantBricks(req, res, next)
        );

        router.get(
            '/keywords/restaurants/:restaurant_id/generate/start',
            authorize(),
            casl(),
            (req: RequestWithPermissions<StartKeywordsGenerationParamsDto, never, never, StartKeywordsGenerationQueryDto>, res, next) =>
                this._keywordsController.handleStartGenerateKeywords(req, res, next)
        );

        router.get('/keywords/restaurants/:restaurant_id/generate/watch', authorize(), (req, res, next) =>
            this._keywordsController.handleWatchKeywordsGeneration(req, res, next)
        );

        router.get(
            '/keywords/restaurants/:restaurant_id/count',
            authorize(),
            (req: Request<GetSelectedKeywordsCountByRestaurantIdParamsDto>, res, next) =>
                this._keywordsController.handleGetSelectedKeywordsCountByRestaurantId(req, res, next)
        );

        router.get('/keywords/restaurants/:restaurant_id', authorize(), (req: Request<GetKeywordsByRestaurantIdParamsDto>, res, next) =>
            this._keywordsController.handleGetKeywordsByRestaurantId(req, res, next)
        );

        router.post(
            '/keywords/restaurants/',
            authorize(),
            (req: Request<false, false, GetKeywordsForRestaurantRequestBodyDto>, res, next) =>
                this._keywordsController.handleGetKeywordsForRestaurants(req, res, next)
        );

        router.post(
            '/keywords/store-locator-organization',
            authorize(),
            (req: Request<false, false, GetStoreLocatorOrganizationKeywordsBodyDto>, res, next) =>
                this._keywordsController.handleGetStoreLocatorOrganizationKeywords(req, res, next)
        );

        router.post(
            '/keywords/restaurants/:restaurant_id/v2',
            authorize(),
            casl(),
            (req: RequestWithPermissions<CreateRestaurantKeywordParamsDto, never, CreateRestaurantKeywordBodyDto>, res, next) =>
                this._keywordsController.handleCreateRestaurantKeyword(req, res, next)
        );

        router.put(
            '/keywords/restaurants/:restaurant_id/select/v2',
            authorize(),
            casl(),
            (req: RequestWithPermissions<SelectRestaurantKeywordsParamsDto, any, SelectRestaurantKeywordsBodyDto>, res, next) =>
                this._keywordsController.handleSelectKeywordsByRestaurantId(req, res, next)
        );

        // @deprecated This route is supposed to be replaced by /rankings/restaurants/:restaurantId/v3
        router.get('/keywords/rankings/restaurants/:restaurant_id/v2', authorize(), (req: any, res, next) =>
            this._keywordsController.handleGetRankingByRestaurantId(req, res, next)
        );

        // @deprecated Use POST /keywords/rankings/restaurants/v3 instead
        router.post('/keywords/rankings/restaurants/:restaurant_id/v3', authorize(), (req, res, next) =>
            this._keywordsController.handleGetKeywordRankingsForOneRestaurantV3(req, res, next)
        );

        router.post('/keywords/rankings/restaurants/v3', authorize(), (req, res, next) =>
            this._keywordsController.handleGetKeywordRankingsForManyRestaurantsV3(req, res, next)
        );

        router.post('/keywords/aggregated-rakings', authorize(), (req, res, next) =>
            this._keywordsController.handleGetAggregatedKeywordRankings(req, res, next)
        );

        router.get('/keywords/:restaurant_keyword_id/rankings/refresh/v2', authorize(), (req, res, next) =>
            this._keywordsController.handleRefreshKeywordRanking(req, res, next)
        );

        router.post('/keywords/restaurants/:restaurant_id/count_and_average_score', authorize(), (req, res, next) =>
            this._keywordsController.handleGetRestaurantKeywordsCountAndAverageScore(req, res, next)
        );

        router.post('/keywords/:restaurant_id/duplicate/v2', authorize(), (req, res, next) =>
            this._keywordsController.handleDuplicateKeywords(req, res, next)
        );

        router.post(
            '/keywords/score',
            authorize(),
            shouldCallMiddleware(cacheMiddleware(CachePrefixKey.KEYWORDS_SCORE, TimeInSeconds.DAY * 5), async (req) => {
                const isFeatureAvailable = await this._experimentationService.isFeatureAvailable('kill-switch-cache-keywords-score');
                if (!isFeatureAvailable) {
                    return false;
                }
                return req.body.shouldCacheScore;
            }),
            (req, res, next) => this._keywordsController.handleProcessKeywordsScore(req, res, next)
        );

        router.put('/keywords/:keyword_id/language', authorize(), (req, res, next) =>
            this._keywordsController.handleUpdateKeywordLanguage(req, res, next)
        );
    }
}
