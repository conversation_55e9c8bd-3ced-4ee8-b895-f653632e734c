import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import {
    AddRestaurantBricksBodyDto,
    addRestaurantBricksBodyValidator,
    AddRestaurantBricksParamsDto,
    addRestaurantBricksParamsValidator,
    AggregatedKeywordRankingsDto,
    BrickDto,
    CreateRestaurantKeywordBodyDto,
    createRestaurantKeywordBodyValidator,
    CreateRestaurantKeywordParamsDto,
    createRestaurantKeywordParamsValidator,
    DuplicateKeywordsBodyDto,
    duplicateKeywordsBodyValidator,
    DuplicateKeywordsParamsDto,
    duplicateKeywordsParamsValidator,
    GeoSampleDto,
    GetAggregatedKeywordRankingsBodyDto,
    getAggregatedKeywordRankingsBodyValidator,
    GetBricksFromBricksGeneratorParamsDto,
    getBricksFromBricksGeneratorParamsValidator,
    getBricksPaginatedQueryValidator,
    GetBricksQueryDto,
    GetKeywordRankingsForManyRestaurantsV3BodyDto,
    getKeywordRankingsForManyRestaurantsV3BodyValidator,
    GetKeywordRankingsForOneRestaurantV3BodyDto,
    getKeywordRankingsForOneRestaurantV3BodyValidator,
    GetKeywordRankingsForOneRestaurantV3ResponseDto,
    GetKeywordsByRestaurantIdParamsDto,
    getKeywordsByRestaurantIdParamsValidator,
    GetKeywordsForRestaurantRequestBodyDto,
    getKeywordsForRestaurantRequestBodyValidator,
    GetRankingsByRestaurantIdParamsDto,
    getRankingsByRestaurantIdParamsValidator,
    GetRankingsByRestaurantIdQueryDto,
    getRankingsByRestaurantIdQueryValidator,
    GetRestaurantKeywordsCountAndAverageScoreBodyDto,
    getRestaurantKeywordsCountAndAverageScoreBodyValidator,
    GetRestaurantKeywordsCountAndAverageScoreParamsDto,
    getRestaurantKeywordsCountAndAverageScoreParamsValidator,
    GetRestaurantsRankingsV3ResponseDto,
    GetSelectedKeywordsCountByRestaurantIdParamsDto,
    getSelectedKeywordsCountByRestaurantIdParamsValidator,
    GetStoreLocatorOrganizationKeywordsBodyDto,
    getStoreLocatorOrganizationKeywordsBodyValidator,
    KeywordsScoreDto,
    ProcessKeywordsScoreBodyDto,
    processKeywordsScoreBodyValidator,
    RefreshRestaurantKeywordRankingParamsDto,
    refreshRestaurantKeywordRankingParamsValidator,
    RestaurantIdParamsDto,
    restaurantIdParamsValidator,
    RestaurantKeywordCountAndAverageScoreDto,
    RestaurantKeywordDto,
    SelectRestaurantKeywordsBodyDto,
    selectRestaurantKeywordsBodyValidator,
    SelectRestaurantKeywordsParamsDto,
    selectRestaurantKeywordsParamsValidator,
    StartKeywordsGenerationParamsDto,
    startKeywordsGenerationParamsValidator,
    StartKeywordsGenerationQueryDto,
    startKeywordsGenerationQueryValidator,
    StartKeywordsGenerationResponseDto,
    StoreLocatorOrganizationKeywordDto,
    UpdateKeywordLanguageBodyDto,
    updateKeywordLanguageBodyValidator,
    UpdateKeywordLanguageParamsDto,
    updateKeywordLanguageParamsValidator,
    UpdateKeywordVolumeFromAdminBodyDto,
    updateKeywordVolumeFromAdminBodyValidator,
    UpdateKeywordVolumeFromAdminParamsDto,
    updateKeywordVolumeFromAdminParamsValidator,
    UpdateRestaurantBricksBodyDto,
    updateRestaurantBricksBodyValidator,
    UpdateRestaurantBricksParamsDto,
    updateRestaurantBricksParamsValidator,
    WatchKeywordsGenerationParamsDto,
    watchKeywordsGenerationParamsValidator,
    WatchKeywordsGenerationResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2, CaslAction, CaslSubject, errorReplacer, MalouErrorCode } from '@malou-io/package-utils';

import { Body, Params, Query } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { RequestWithPermissions } from ':helpers/utils.types';
import AddRestaurantBricksUseCase from ':modules/keywords/use-cases/add-restaurant-bricks/add-restaurant-bricks.use-case';
import { CreateRestaurantKeywordUseCase } from ':modules/keywords/use-cases/create-restaurant-keyword/create-restaurant-keyword.use-case';
import { DuplicateKeywordsUseCase } from ':modules/keywords/use-cases/duplicate-keywords/duplicate-keywords.use-case';
import { GetAggregatedKeywordRankingsUseCase } from ':modules/keywords/use-cases/get-aggregated-keyword-rankings/get-aggregated-keyword-rankings.use-case';
import { GetBricksCountUseCase } from ':modules/keywords/use-cases/get-bricks-count/get-bricks-count.use-case';
import { GetBricksFromBricksGeneratorUseCase } from ':modules/keywords/use-cases/get-bricks-from-bricks-generator/get-bricks-from-bricks-generator.use-case';
import { GetBricksPaginatedUseCase } from ':modules/keywords/use-cases/get-bricks-paginated/get-bricks-paginated.use-case';
import { GetKeywordsByRestaurantIdUseCase } from ':modules/keywords/use-cases/get-keywords-by-restaurant-id/get-keywords-by-restaurant-id.use-case';
import { GetRestaurantKeywordCountAndAverageScoreUseCase } from ':modules/keywords/use-cases/get-restaurant-keywords-count-and-average-score/get-restaurant-keywords-count-and-average-score.use-case';
import { GetSelectedKeywordsCountByRestaurantIdUseCase } from ':modules/keywords/use-cases/get-selected-keywords-count-by-restaurant-id/get-selected-keywords-count-by-restaurant-id.use-case';
import { ProcessKeywordsScoreUseCase } from ':modules/keywords/use-cases/process-keywords-score/process-keywords-score.use-case';
import { RefreshRestaurantKeywordRankingUseCase } from ':modules/keywords/use-cases/refresh-restaurant-keyword-ranking/refresh-restaurant-keyword-ranking.use-case';
import { UpdateKeywordLanguageUseCase } from ':modules/keywords/use-cases/update-keyword-language/update-keyword-language.use-case';
import { UpdateKeywordVolumeFromAdminUseCase } from ':modules/keywords/use-cases/update-keyword-volume-from-admin/update-keyword-volume-from-admin.use-case';
import UpdateRestaurantBricksUseCase from ':modules/keywords/use-cases/update-restaurant-bricks/update-restaurant-bricks.use-case';

import { StartKeywordsGenerationUseCase } from './use-cases/generate-keywords/start-keywords-generation.use-case';
import { WatchKeywordsGenerationUseCase } from './use-cases/generate-keywords/watch-keywords-generation.use-case';
import { GetKeywordRankingsForManyRestaurantsV3UseCase } from './use-cases/get-keyword-ranking-for-many-restaurants-v3.use-case';
import { GetKeywordsForRestaurantsUseCase } from './use-cases/get-keywords-for-restaurants/get-keywords-for-restaurants.use-case';
import { GetKeywordsRankingsByRestaurantIdUseCase } from './use-cases/get-keywords-rankings-by-restaurant-id/get-keywords-rankings-by-restaurant-id.use-case';
import { GetStoreLocatorOrganizationKeywordsUseCase } from './use-cases/get-store-locator-organization-keywords/get-store-locator-organization-keywords.use-case';
import { SelectRestaurantKeywordsUseCase } from './use-cases/select-restaurant-keywords/select-restaurant-keywords.use-case';

@singleton()
export default class KeywordsController {
    constructor(
        private readonly _addRestaurantBricksUseCase: AddRestaurantBricksUseCase,
        private readonly _createRestaurantKeywordUseCase: CreateRestaurantKeywordUseCase,
        private readonly _duplicateKeywordsUseCase: DuplicateKeywordsUseCase,
        private readonly _getBricksFromBricksGeneratorUseCase: GetBricksFromBricksGeneratorUseCase,
        private readonly _getBricksCountUseCase: GetBricksCountUseCase,
        private readonly _getBricksPaginatedUseCase: GetBricksPaginatedUseCase,
        private readonly _getKeywordsByRestaurantIdUseCase: GetKeywordsByRestaurantIdUseCase,
        private readonly _getKeywordsForRestaurantsUseCase: GetKeywordsForRestaurantsUseCase,
        private readonly _getKeywordRankingsForManyRestaurantsV3UseCase: GetKeywordRankingsForManyRestaurantsV3UseCase,
        private readonly _getRestaurantKeywordCountAndAverageScoreUseCase: GetRestaurantKeywordCountAndAverageScoreUseCase,
        private readonly _getSelectedKeywordsCountByRestaurantIdUseCase: GetSelectedKeywordsCountByRestaurantIdUseCase,
        private readonly _getStoreLocatorOrganizationKeywordsUseCase: GetStoreLocatorOrganizationKeywordsUseCase,
        private readonly _processKeywordsScoreUseCase: ProcessKeywordsScoreUseCase,
        private readonly _refreshRestaurantKeywordRankingUseCase: RefreshRestaurantKeywordRankingUseCase,
        private readonly _updateKeywordLanguageUseCase: UpdateKeywordLanguageUseCase,
        private readonly _updateKeywordVolumeFromAdminUseCase: UpdateKeywordVolumeFromAdminUseCase,
        private readonly _updateRestaurantBricksUseCase: UpdateRestaurantBricksUseCase,
        private readonly _getKeywordsRankingsByRestaurantIdUseCase: GetKeywordsRankingsByRestaurantIdUseCase,
        private readonly _selectRestaurantKeywordsUseCase: SelectRestaurantKeywordsUseCase,
        private readonly _startKeywordsGenerationUseCase: StartKeywordsGenerationUseCase,
        private readonly _watchKeywordsGenerationUseCase: WatchKeywordsGenerationUseCase,
        private readonly _getAggregatedKeywordRankingsUseCase: GetAggregatedKeywordRankingsUseCase
    ) {}

    @Query(getBricksPaginatedQueryValidator)
    async handleGetBricksPaginated(
        req: Request<never, never, never, GetBricksQueryDto>,
        res: Response<ApiResultV2<BrickDto[]>>,
        next: NextFunction
    ) {
        try {
            const { pageNumber, pageSize, total } = req.query;

            const bricks = await this._getBricksPaginatedUseCase.execute({ pageNumber, pageSize, total });

            return res.json({ data: bricks });
        } catch (err) {
            next(err);
        }
    }

    async handleGetBricksCount(_req: Request, res: Response<ApiResultV2<number>>, next: NextFunction) {
        try {
            const count = await this._getBricksCountUseCase.execute();
            return res.json({ data: count });
        } catch (e) {
            next(e);
        }
    }

    @Params(getBricksFromBricksGeneratorParamsValidator)
    async handleGetBricksGenerator(
        req: Request<GetBricksFromBricksGeneratorParamsDto>,
        res: Response<ApiResultV2<BrickDto[]>>,
        next: NextFunction
    ) {
        try {
            const { key, type } = req.params;
            const bricks = await this._getBricksFromBricksGeneratorUseCase.execute(key, type);
            res.json({ data: bricks });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateRestaurantBricksParamsValidator)
    @Body(updateRestaurantBricksBodyValidator)
    async handleUpdateRestaurantBricks(
        req: RequestWithPermissions<UpdateRestaurantBricksParamsDto, any, UpdateRestaurantBricksBodyDto>,
        res: Response<ApiResultV2<never>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { formData } = req.body;
            if (!formData) {
                return { error: true, message: MalouErrorCode.MISSING_PARAM };
            }
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.RESTAURANT, { _id: restaurantId }),
                'bricks'
            );

            await this._updateRestaurantBricksUseCase.execute(restaurantId, formData);
            return res.json({ msg: 'Success' });
        } catch (err) {
            next(err);
        }
    }

    @Params(addRestaurantBricksParamsValidator)
    @Body(addRestaurantBricksBodyValidator)
    async handleAddRestaurantBricks(
        req: RequestWithPermissions<AddRestaurantBricksParamsDto, any, AddRestaurantBricksBodyDto>,
        res: Response<ApiResultV2<never>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { formData } = req.body;

            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.RESTAURANT, { _id: restaurantId }),
                'bricks'
            );

            await this._addRestaurantBricksUseCase.execute(restaurantId, formData);
            return res.json({ msg: 'Success' });
        } catch (err) {
            next(err);
        }
    }

    @Params(createRestaurantKeywordParamsValidator)
    @Body(createRestaurantKeywordBodyValidator)
    async handleCreateRestaurantKeyword(
        req: RequestWithPermissions<CreateRestaurantKeywordParamsDto, never, CreateRestaurantKeywordBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const keyword = req.body;
            const { restaurantId } = req.params;
            const user = req.user;
            assert(user, 'User not found');

            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.CREATE,
                subject(CaslSubject.KEYWORD, { restaurantId })
            );

            const restaurantKeywordDto = await this._createRestaurantKeywordUseCase.execute(restaurantId, keyword, user._id.toString());
            return res.json({ msg: 'Keyword created', data: restaurantKeywordDto });
        } catch (err) {
            return next(err);
        }
    }

    @Params(getSelectedKeywordsCountByRestaurantIdParamsValidator)
    async handleGetSelectedKeywordsCountByRestaurantId(
        req: Request<GetSelectedKeywordsCountByRestaurantIdParamsDto>,
        res: Response<ApiResultV2<number>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const count = await this._getSelectedKeywordsCountByRestaurantIdUseCase.execute(restaurantId);
            return res.json({ data: count });
        } catch (err) {
            next(err);
        }
    }

    @Params(getRestaurantKeywordsCountAndAverageScoreParamsValidator)
    @Body(getRestaurantKeywordsCountAndAverageScoreBodyValidator)
    async handleGetRestaurantKeywordsCountAndAverageScore(
        req: Request<GetRestaurantKeywordsCountAndAverageScoreParamsDto, never, GetRestaurantKeywordsCountAndAverageScoreBodyDto>,
        res: Response<ApiResultV2<RestaurantKeywordCountAndAverageScoreDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, previousPeriod } = req.body;
            const result = await this._getRestaurantKeywordCountAndAverageScoreUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                previousPeriod,
            });
            res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(processKeywordsScoreBodyValidator)
    async handleProcessKeywordsScore(
        req: Request<any, any, ProcessKeywordsScoreBodyDto>,
        res: Response<ApiResultV2<KeywordsScoreDto>>,
        next: NextFunction
    ) {
        try {
            const body = req.body;
            const result = await this._processKeywordsScoreUseCase.execute(body);
            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateKeywordVolumeFromAdminParamsValidator)
    @Body(updateKeywordVolumeFromAdminBodyValidator)
    async handleUpdateKeywordVolumeFromAdmin(
        req: Request<UpdateKeywordVolumeFromAdminParamsDto, any, UpdateKeywordVolumeFromAdminBodyDto>,
        res: Response<ApiResultV2<RestaurantKeywordDto>>,
        next: NextFunction
    ) {
        try {
            const { keywordId } = req.params;
            const { volumeFromAdmin, restaurantId } = req.body;
            const restaurantKeyword = await this._updateKeywordVolumeFromAdminUseCase.execute(keywordId, volumeFromAdmin, restaurantId);
            return res.json({ data: restaurantKeyword });
        } catch (err) {
            next(err);
        }
    }

    @Params(startKeywordsGenerationParamsValidator)
    @Query(startKeywordsGenerationQueryValidator)
    async handleStartGenerateKeywords(
        req: RequestWithPermissions<StartKeywordsGenerationParamsDto, never, never, StartKeywordsGenerationQueryDto>,
        res: Response<ApiResultV2<StartKeywordsGenerationResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { langs } = req.query;
            assert(req.user, 'User not found');
            const userId = req.user._id.toString();
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.KEYWORD, { restaurantId })
            );

            const status = await this._startKeywordsGenerationUseCase.execute(restaurantId, langs, userId);

            return res.json({ data: status });
        } catch (err) {
            next(err);
        }
    }

    @Params(watchKeywordsGenerationParamsValidator)
    async handleWatchKeywordsGeneration(
        req: Request<WatchKeywordsGenerationParamsDto>,
        res: Response<ApiResultV2<WatchKeywordsGenerationResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const result = await this._watchKeywordsGenerationUseCase.execute(restaurantId);
            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(duplicateKeywordsParamsValidator)
    @Body(duplicateKeywordsBodyValidator)
    async handleDuplicateKeywords(
        req: Request<DuplicateKeywordsParamsDto, null, DuplicateKeywordsBodyDto>,
        res: Response<ApiResultV2<never>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { restaurantIds, originalKeywordIds } = req.body;
            await this._duplicateKeywordsUseCase.execute(restaurantId, originalKeywordIds, restaurantIds);
            return res.json({ msg: 'Keywords duplicated' });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateKeywordLanguageParamsValidator)
    @Body(updateKeywordLanguageBodyValidator)
    async handleUpdateKeywordLanguage(
        req: Request<UpdateKeywordLanguageParamsDto, never, UpdateKeywordLanguageBodyDto>,
        res: Response<ApiResultV2<RestaurantKeywordDto>>,
        next: NextFunction
    ) {
        try {
            const { keywordId } = req.params;
            const { language, restaurantId } = req.body;
            const userId = req.user._id.toString();
            const keyword = await this._updateKeywordLanguageUseCase.execute(keywordId, restaurantId, language, userId);
            return res.json({ data: keyword });
        } catch (err) {
            next(err);
        }
    }

    @Params(refreshRestaurantKeywordRankingParamsValidator)
    async handleRefreshKeywordRanking(
        req: Request<RefreshRestaurantKeywordRankingParamsDto>,
        res: Response<ApiResultV2<{ result: GeoSampleDto[] }>>,
        next: NextFunction
    ) {
        try {
            const { restaurantKeywordId } = req.params;
            const geoSamples = await this._refreshRestaurantKeywordRankingUseCase.execute(restaurantKeywordId);

            return res.json({ data: { result: geoSamples } });
        } catch (e) {
            next(e);
        }
    }

    @Params(getKeywordsByRestaurantIdParamsValidator)
    async handleGetKeywordsByRestaurantId(
        req: Request<GetKeywordsByRestaurantIdParamsDto>,
        res: Response<ApiResultV2<RestaurantKeywordDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const keywords = await this._getKeywordsByRestaurantIdUseCase.execute(restaurantId);

            return res.json({ data: keywords });
        } catch (err) {
            return next(err);
        }
    }

    @Body(getKeywordsForRestaurantRequestBodyValidator)
    async handleGetKeywordsForRestaurants(
        req: Request<false, false, GetKeywordsForRestaurantRequestBodyDto>,
        res: Response<ApiResultV2<RestaurantKeywordDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds } = req.body;
            const keywords = await this._getKeywordsForRestaurantsUseCase.execute(restaurantIds);

            return res.json({ data: keywords });
        } catch (err) {
            return next(err);
        }
    }

    @Body(getStoreLocatorOrganizationKeywordsBodyValidator)
    async handleGetStoreLocatorOrganizationKeywords(
        req: Request<false, false, GetStoreLocatorOrganizationKeywordsBodyDto>,
        res: Response<ApiResultV2<StoreLocatorOrganizationKeywordDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds } = req.body;
            const keywords = await this._getStoreLocatorOrganizationKeywordsUseCase.execute(restaurantIds);

            return res.json({ data: keywords });
        } catch (err) {
            return next(err);
        }
    }

    /**
     * @deprecated This route is supposed to be replaced by /rankings/restaurants/:restaurantId/v3
     */
    @Params(getRankingsByRestaurantIdParamsValidator)
    @Query(getRankingsByRestaurantIdQueryValidator)
    async handleGetRankingByRestaurantId(
        req: Request<GetRankingsByRestaurantIdParamsDto, never, never, GetRankingsByRestaurantIdQueryDto>,
        res: Response<ApiResultV2<GeoSampleDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, previousPeriod } = req.query;
            const result = await this._getKeywordsRankingsByRestaurantIdUseCase.execute(restaurantId, startDate, endDate, previousPeriod);
            res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(restaurantIdParamsValidator)
    @Body(getKeywordRankingsForOneRestaurantV3BodyValidator)
    async handleGetKeywordRankingsForOneRestaurantV3(
        req: Request<
            RestaurantIdParamsDto,
            GetKeywordRankingsForOneRestaurantV3ResponseDto,
            Required<GetKeywordRankingsForOneRestaurantV3BodyDto>,
            never,
            never
        >,
        res: Response<ApiResultV2<GetKeywordRankingsForOneRestaurantV3ResponseDto>>,
        next: NextFunction
    ) {
        try {
            const result = await this._getKeywordRankingsForManyRestaurantsV3UseCase.execute({
                ...req.body,
                restaurantIds: [req.params.restaurant_id],
            });
            return res.json({ data: result.restaurants[0] });
        } catch (err) {
            return next(err);
        }
    }

    @Body(getKeywordRankingsForManyRestaurantsV3BodyValidator)
    async handleGetKeywordRankingsForManyRestaurantsV3(
        req: Request<
            RestaurantIdParamsDto,
            GetRestaurantsRankingsV3ResponseDto,
            Required<GetKeywordRankingsForManyRestaurantsV3BodyDto>,
            never,
            never
        >,
        res: Response<ApiResultV2<GetRestaurantsRankingsV3ResponseDto>>,
        next: NextFunction
    ) {
        try {
            const data = await this._getKeywordRankingsForManyRestaurantsV3UseCase.execute(req.body);
            return res.json({ data });
        } catch (err) {
            logger.error('[ERROR_AGGREGATED_STATS] [KEYWORDS]', {
                error: JSON.stringify(err, errorReplacer),
                body: JSON.stringify(req.body),
            });
            return next(err);
        }
    }

    @Body(getAggregatedKeywordRankingsBodyValidator)
    async handleGetAggregatedKeywordRankings(
        req: Request<never, never, GetAggregatedKeywordRankingsBodyDto>,
        res: Response<ApiResultV2<AggregatedKeywordRankingsDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds, startDate, endDate, platformKey, comparisonPeriod } = req.body;
            const data = await this._getAggregatedKeywordRankingsUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                platformKey,
                comparisonPeriod,
            });
            return res.json({ data });
        } catch (err) {
            return next(err);
        }
    }

    @Params(selectRestaurantKeywordsParamsValidator)
    @Body(selectRestaurantKeywordsBodyValidator)
    async handleSelectKeywordsByRestaurantId(
        req: RequestWithPermissions<SelectRestaurantKeywordsParamsDto, any, SelectRestaurantKeywordsBodyDto>,
        res: Response<ApiResultV2<RestaurantKeywordDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { keywordIds } = req.body;
            assert(req.user, 'User not found');
            const { _id: userId } = req.user;

            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.MANAGE,
                subject(CaslSubject.KEYWORD, { restaurantId })
            );
            const restaurantKeywordsDto = await this._selectRestaurantKeywordsUseCase.execute(restaurantId, keywordIds, userId.toString());
            res.json({ data: restaurantKeywordsDto });
        } catch (error) {
            next(error);
        }
    }
}
