import { container } from 'tsyringe';

import { StoreLocatorOrganizationKeywordDto } from '@malou-io/package-dto';
import { DbId } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

import { GetStoreLocatorOrganizationKeywordsUseCase } from './get-store-locator-organization-keywords.use-case';

describe('GetStoreLocatorOrganizationKeywordsUseCase', () => {
    beforeAll(() => {
        registerRepositories(['KeywordsTempRepository', 'RestaurantKeywordsRepository', 'RestaurantsRepository']);
    });

    describe('execute', () => {
        it('should return store locator organization keywords for given restaurant IDs', async () => {
            const getStoreLocatorOrganizationKeywordsUseCase = container.resolve(GetStoreLocatorOrganizationKeywordsUseCase);

            const testCase = new TestCaseBuilderV2<'keywordsTemp' | 'restaurants' | 'restaurantKeywords'>({
                seeds: {
                    keywordsTemp: {
                        data() {
                            return [
                                getDefaultKeywordTemp().text('pizza delivery').build(),
                                getDefaultKeywordTemp().text('burger restaurant').build(),
                                getDefaultKeywordTemp().text('sushi place').build(),
                                getDefaultKeywordTemp().text('vegan options').build(),
                            ];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().internalName('restaurant 1').uniqueKey('uniqueKey_1').build(),
                                getDefaultRestaurant().internalName('restaurant 2').uniqueKey('uniqueKey_2').build(),
                                getDefaultRestaurant().internalName('restaurant 3').uniqueKey('uniqueKey_3').build(),
                            ];
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .keywordId(dependencies.keywordsTemp()[0]._id)
                                    .selected(true)
                                    .build(),
                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .keywordId(dependencies.keywordsTemp()[1]._id)
                                    .selected(true)
                                    .build(),

                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .keywordId(dependencies.keywordsTemp()[2]._id)
                                    .selected(false)
                                    .build(),

                                getDefaultRestaurantKeyword()
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .keywordId(dependencies.keywordsTemp()[3]._id)
                                    .selected(true)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoreLocatorOrganizationKeywordDto[] {
                    return [
                        {
                            restaurantKeywordId: dependencies.restaurantKeywords[0]._id.toString(),
                            keywordId: dependencies.restaurantKeywords[0].keywordId.toString(),
                            restaurantId: dependencies.restaurantKeywords[0].restaurantId.toString(),
                            text: dependencies.keywordsTemp[0].text,
                        },
                        {
                            restaurantKeywordId: dependencies.restaurantKeywords[1]._id.toString(),
                            keywordId: dependencies.restaurantKeywords[1].keywordId.toString(),
                            restaurantId: dependencies.restaurantKeywords[1].restaurantId.toString(),
                            text: dependencies.keywordsTemp[1].text,
                        },
                    ];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = [seededObjects.restaurants[0]._id.toString(), seededObjects.restaurants[1]._id.toString()];

            const result = await getStoreLocatorOrganizationKeywordsUseCase.execute(restaurantIds);

            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should return empty array when no restaurant keywords found', async () => {
            const getStoreLocatorOrganizationKeywordsUseCase = container.resolve(GetStoreLocatorOrganizationKeywordsUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                },
                expectedResult(): StoreLocatorOrganizationKeywordDto[] {
                    return [];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await getStoreLocatorOrganizationKeywordsUseCase.execute(restaurantIds);

            expect(result).toEqual(expectedResult);
        });
    });
});
