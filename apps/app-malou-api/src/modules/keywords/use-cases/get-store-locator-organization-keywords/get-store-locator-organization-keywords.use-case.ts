import { singleton } from 'tsyringe';

import { StoreLocatorOrganizationKeywordDto } from '@malou-io/package-dto';

import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';

@singleton()
export class GetStoreLocatorOrganizationKeywordsUseCase {
    constructor(private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository) {}

    async execute(restaurantIds: string[]): Promise<StoreLocatorOrganizationKeywordDto[]> {
        const restaurantKeywords = await this._restaurantKeywordsRepository.findSelectedRestaurantKeywordsByRestaurantIds(restaurantIds);

        return restaurantKeywords.map((restaurantKeyword) => ({
            restaurantKeywordId: restaurantKeyword.id,
            keywordId: restaurantKeyword.keywordId,
            restaurantId: restaurantKeyword.restaurantId,
            text: restaurantKeyword.keyword.text,
        }));
    }
}
