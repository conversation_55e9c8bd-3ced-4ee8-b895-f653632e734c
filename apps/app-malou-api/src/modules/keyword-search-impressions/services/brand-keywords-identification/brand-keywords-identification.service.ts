import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { AiInteractionRelatedEntityCollection, AiInteractionType, isNotNil } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import {
    AiBrandKeywordsIdentificationService,
    BrandKeywordsIdentificationPayload,
} from ':microservices/ai-brand-keywords-identification.service';
import { SearchKeywordsImpressionsInvocationType } from ':microservices/search-keywords-impressions.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class BrandKeywordsIdentificationService {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _aiBrandKeywordsIdentificationService: AiBrandKeywordsIdentificationService,
        private readonly _aiInteractionsRepository: AiInteractionsRepository
    ) {}

    async identifyBrandKeywordsForRestaurant(restaurantId: string): Promise<{ brandNameKeywords: string[]; brandGroupKeywords: string[] }> {
        const aiInteraction = await this._aiInteractionsRepository.create({
            data: {
                type: AiInteractionType.BRAND_KEYWORDS_IDENTIFICATION,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.KEYWORDS,
                restaurantId: toDbId(restaurantId),
                userId: undefined,
            },
        });

        try {
            const restaurantData = await this._getRestaurantData(restaurantId);

            const { aiResponse, aiInteractionDetails } =
                await this._aiBrandKeywordsIdentificationService.processBrandKeywordsIdentification({
                    restaurantData,
                    type: SearchKeywordsImpressionsInvocationType.BRAND_KEYWORDS_IDENTIFICATION,
                });

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: toDbId(restaurantId),
                feature: AiInteractionType.BRAND_KEYWORDS_IDENTIFICATION,
            });

            const { brandNameKeywords, brandGroupKeywords } = aiResponse;

            if (isNotNil(aiInteractionDetails)) {
                const aiInteractionUpdate = aiInteractionDetails[0];
                await this._aiInteractionsRepository.findOneAndUpdate({
                    filter: { _id: aiInteraction._id },
                    update: aiInteractionUpdate,
                    options: { new: true },
                });
            }

            await this._restaurantsRepository.findOneAndUpdate({
                filter: { _id: toDbId(restaurantId) },
                update: {
                    brandKeywords: { brandNameKeywords, brandGroupKeywords },
                },
            });

            return { brandNameKeywords, brandGroupKeywords };
        } catch (error: any) {
            logger.error('[BrandKeywordsIdentificationService] Error identifying brand keywords', { restaurantId, error });
            await this._aiInteractionsRepository.findOneAndUpdateOrFail({
                filter: { _id: aiInteraction._id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _getRestaurantData(restaurantId: string): Promise<BrandKeywordsIdentificationPayload['restaurantData']> {
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            options: { lean: true },
            projection: { name: 1, organizationId: 1, address: 1 },
        });
        const organization = await this._organizationsRepository.findOneOrFail({
            filter: { _id: restaurant.organizationId },
            options: { lean: true },
            projection: { name: 1 },
        });

        return {
            restaurantName: restaurant.name,
            organizationName: organization.name,
            formattedAddress: restaurant.address?.formattedAddress ?? '',
            locality: restaurant.address?.locality ?? '',
            country: restaurant.address?.country ?? '',
        };
    }
}
