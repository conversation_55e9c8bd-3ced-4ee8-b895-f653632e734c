import { z } from 'zod';

import { InsightType } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { MetaGraphApiCredentialsHandlerError } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';

export const MetaGraphApiHelperEndpoint = {
    CREATE_IG_CONTAINER: 'CREATE_IG_CONTAINER',
    CREATE_PAGE_VIDEO_REEL: 'CREATE_PAGE_VIDEO_REEL',
    FETCH_FACEBOOK_POST: 'FETCH_FACEBOOK_POST',
    FETCH_PAGE_LOCATION: 'FETCH_PAGE_LOCATION',
    GET_FB_PAGE_POST: 'GET_FB_PAGE_POST',
    GET_FB_VIDEO: 'GET_FB_VIDEO',
    GET_IG_CONTAINER: 'GET_IG_CONTAINER',
    GET_IG_MEDIA: 'GET_IG_MEDIA',
    PUBLISH_FB_VIDEO_POST: 'PUBLISH_FB_VIDEO_POST',
    PUBLISH_IG_CONTAINER: 'PUBLISH_IG_CONTAINER',
    PUBLISH_POST_WITH_UNPUBLISHED_PHOTOS: 'PUBLISH_POST_WITH_UNPUBLISHED_PHOTOS',
    SEARCH_IG_ACCOUNT: 'SEARCH_IG_ACCOUNT',
    UPLOAD_PAGE_VIDEO_REEL: 'UPLOAD_PAGE_VIDEO_REEL',
    UPLOAD_UNPUBLISHED_PHOTO: 'UPLOAD_UNPUBLISHED_PHOTO',
} as const;

export type IMetaGraphApiHelperEndpoint = (typeof MetaGraphApiHelperEndpoint)[keyof typeof MetaGraphApiHelperEndpoint];

export type MetaGraphApiHelperErrorObject = MetaGraphApiCredentialsHandlerError & {
    endpoint: IMetaGraphApiHelperEndpoint;
};

const datumFromValidator = z.object({
    name: z.string().optional(),
    id: z.string().optional(),
});

const summaryValidator = z.object({
    total_count: z.number().optional(),
});

const commentValidator = z.object({
    like_count: z.number().optional(),
    id: z.string().optional(),
    from: datumFromValidator.optional(),
    message: z.string().optional(),
    created_time: z.string().optional(),
    comments: z.lazy(() => commentsValidator.optional()),
});

const commentsValidator = z.object({
    data: z.array(commentValidator).optional(),
    summary: summaryValidator.optional(),
});

const mediaValidator = z.object({
    image: z
        .object({
            height: z.number().optional(),
            width: z.number().optional(),
            src: z.string().optional(),
        })
        .optional(),
    source: z.string().optional(),
});

const attachmentValidator = z.object({
    media: mediaValidator.optional(),
    media_type: z.string().optional(),
    type: z.string().optional(),
    subattachments: z
        .object({
            data: z.array(z.lazy(() => attachmentValidator.optional())).optional(),
        })
        .optional(),
    target: z
        .object({
            id: z.string().optional(),
        })
        .optional(),
});

const likesValidator = z.object({
    summary: summaryValidator.optional(),
});

const insightsValidator = z.object({
    data: z.array(
        z.object({
            name: z.nativeEnum(InsightType).optional(),
            values: z.array(
                z.object({
                    value: z.number(),
                })
            ),
        })
    ),
});

const userValidator = z.object({
    name: z.string().optional(),
    id: z.string().optional(),
    picture: z
        .object({
            data: z
                .object({
                    height: z.number(),
                    is_silhouette: z.boolean().optional(),
                    url: z.string().optional(),
                    width: z.number().optional(),
                })
                .optional(),
        })
        .optional(),
});

export const uploadUnpublishedPhotoResponseValidator = z.object({ id: z.string() });

export const publishPostWithUnpublishedPhotosResponseValidator = z.object({ id: z.string() });

export const fetchPageLocationResponseValidator = z.object({
    id: z.string(),
    name: z.string(),
    link: z.string(),
    location: z
        .object({
            city: z.string().optional(),
            country: z.string().optional(),
            latitude: z.number().optional(),
            longitude: z.number().optional(),
            state: z.string().optional(),
            street: z.string().optional(),
            zip: z.string().optional(),
        })
        .optional(),
});
export const searchIgAccountResponseValidator = z.object({
    id: z.string(),
    business_discovery: z.object({
        followers_count: z.number(),
        username: z.string(),
        media_count: z.number().optional(),
        profile_picture_url: z.string().optional(),
        biography: z.string().optional(),
        name: z.string().optional(),
    }),
});
export const fetchFacebookPostResponseValidator = z.object({
    id: z.string(),
    from: userValidator.optional(),
    message: z.string().optional(),
    story: z.string().optional(),
    created_time: z.string(),
    updated_time: z.string().optional(),
    status_type: z.string().optional(),
    permalink_url: z.string(),
    attachments: z.object({ data: z.array(attachmentValidator).optional() }).optional(),
    comments: commentsValidator.optional(),
    username: z.string().optional(),
    likes: likesValidator.optional(),
    insights: insightsValidator.optional(),
    shares: z.object({ count: z.number() }).optional(),
});

export type FetchPageLocationResponse = z.infer<typeof fetchPageLocationResponseValidator>;

export const publishFbVideoPostResponseValidator = z.object({ id: z.string() });

// Page Video Reels
// POST /{page_id}/video_reels
export const postFbVideoReelsResponseValidator = z.object({
    video_id: z.string().optional(),
    upload_url: z.string().optional(),
});

export const getFbVideoFields = 'id,post_id,status';
export const getFbVideoResponseValidator = z.object({
    id: z.string(),
    post_id: z.string(),
    status: z
        .object({
            video_status: z.enum(['error', 'expired', 'processing', 'ready', 'uploading', 'upload_failed', 'upload_complete']),

            publishing_phase: z
                .object({
                    error: z.object({ code: z.number(), message: z.string() }).passthrough().optional(),
                    publish_status: z.enum(['draft', 'error', 'published', 'scheduled']).optional(),
                })
                .passthrough()
                .optional(),

            processing_phase: z
                .object({
                    // The doc says there’s a field named `error` (singular) but the doc is wrong.
                    // This is actually a list. Thank you Zuckerberg.
                    errors: z.array(z.object({ code: z.number(), message: z.string() }).passthrough()).optional(),
                })
                .passthrough()
                .optional(),

            uploading_phase: z
                .object({
                    errors: z.array(z.object({ code: z.number(), message: z.string() }).passthrough()).optional(),
                })
                .passthrough()
                .optional(),
        })
        .describe(
            'https://developers.facebook.com/docs/graph-api/reference/video-status/#overview https://developers.facebook.com/docs/video-api/guides/reels-publishing/#step-3--publish-the-reel'
        ),
});

export const getFbPagePostFields = 'id,created_time,updated_time,permalink_url,message,attachments{media_type,media,subattachments}';
const getFbPagePostAttachmentMediaValidator = z.object({ image: z.object({ src: z.string() }) });
const getFbPagePostAttachmentPhotoValidator = z.object({ media_type: z.literal('photo'), media: getFbPagePostAttachmentMediaValidator });
const getFbPagePostAttachmentVideoValidator = z.object({
    media_type: z.literal('video'),
    media: getFbPagePostAttachmentMediaValidator.merge(z.object({ source: z.string() })),
});
const getFbPagePostSubAttachmentPhotoValidator = z.object({ type: z.literal('photo'), media: getFbPagePostAttachmentMediaValidator });
const getFbPagePostSubAttachmentVideoValidator = z.object({
    type: z.literal('video'),
    media: getFbPagePostAttachmentMediaValidator.merge(z.object({ source: z.string() })),
});
// For album with only Videos (not possible via the api but possible via facebook.com), we don't got the root 'media' attribute'.
// If you want it, just take the 'media' attribute from the first 'subattachments'.
const getFbPagePostAttachmentAlbumValidator = z.object({
    media_type: z.literal('album'),
    media: getFbPagePostAttachmentMediaValidator.optional(),
    subattachments: z.object({
        data: z.array(z.union([getFbPagePostSubAttachmentPhotoValidator, getFbPagePostSubAttachmentVideoValidator])),
    }),
});

const getFbPagePostAttachmentValidator = z.union([
    getFbPagePostAttachmentPhotoValidator,
    getFbPagePostAttachmentVideoValidator,
    getFbPagePostAttachmentAlbumValidator,
]);
export const getFbPagePostResponseValidator = z.object({
    id: z.string(),
    created_time: z.string().datetime({ offset: true }),
    updated_time: z.string().datetime({ offset: true }),
    permalink_url: z.string(),
    message: z.string().optional(),
    attachments: z.object({
        data: z.array(getFbPagePostAttachmentValidator),
    }),
});
export type GetFbPagePostResponse = z.infer<typeof getFbPagePostResponseValidator>;

export const createIgContainerValidator = z.object({ id: z.string() });

export const getIgContainerFields = 'id,status_code,status';
export const getIgContainerValidator = z.object({
    id: z.string(),
    status_code: z.enum(['EXPIRED', 'ERROR', 'FINISHED', 'IN_PROGRESS', 'PUBLISHED']),
    status: z.string().optional(),
});

export const publishIgContainerValidator = z.object({ id: z.string() });

export const getIgMediaFields =
    'id,timestamp,permalink,caption,media_type,media_product_type,media_url,thumbnail_url,children{media_type,media_url,thumbnail_url}';
const getIGMediaPhotoChildValidator = z.object({
    media_type: z.literal('IMAGE'),
    media_url: z.string(),
});
const getIgMediaVideoChildValidator = z.object({
    media_type: z.literal('VIDEO'),
    media_url: z.string(),
    thumbnail_url: z.string(),
});
const getIGMediaChildValidator = z.union([getIGMediaPhotoChildValidator, getIgMediaVideoChildValidator]);
const getIGMediaBaseValidator = z.object({
    id: z.string(),
    timestamp: z.string().datetime({ offset: true }),
    permalink: z.string(),
    caption: z.string().optional(),
    media_url: z.string(),
});
const getIGMediaImageValidator = getIGMediaBaseValidator.merge(
    z.object({
        media_type: z.literal('IMAGE'),
        media_product_type: z.literal('FEED'),
    })
);
const getIGMediaCarouselValidator = getIGMediaBaseValidator.merge(
    z.object({
        media_type: z.literal('CAROUSEL_ALBUM'),
        media_product_type: z.literal('FEED'),
        children: z.object({ data: z.array(getIGMediaChildValidator) }),
    })
);
const getIGMediaReelValidator = getIGMediaBaseValidator.merge(
    z.object({
        media_type: z.literal('VIDEO'),
        media_product_type: z.literal('REELS'),
        thumbnail_url: z.string(),
    })
);
export const getIGMediaValidator = z.union([getIGMediaImageValidator, getIGMediaCarouselValidator, getIGMediaReelValidator]);
export type GetIgMediaResponse = z.infer<typeof getIGMediaValidator>;
