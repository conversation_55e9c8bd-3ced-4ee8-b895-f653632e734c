import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import {
    createSocialPostBodyValidator,
    CreateSocialPostDto,
    DeleteSocialPostsBodyDto,
    deleteSocialPostsBodyValidator,
    DuplicatePostV2BodyDto,
    duplicatePostV2BodyValidator,
    DuplicatePostV2ParamsDto,
    duplicatePostV2ParamsValidator,
    DuplicatePostV2ResponseDto,
    FeedItemDto,
    GetAllPostIdsFromCurrentFilterParamsDto,
    getAllPostIdsFromCurrentFilterParamsValidator,
    GetAllPostIdsFromCurrentFilterQueryDto,
    getAllPostIdsFromCurrentFilterQueryValidator,
    GetFeedParamsDto,
    getFeedParamsValidator,
    GetFeedQueryDto,
    getFeedQueryValidator,
    GetPostsToDuplicateQueryDto,
    getPostsToDuplicateQueryValidator,
    GetProgrammedSocialPostPlatformKeysByDateParamsDto,
    getProgrammedSocialPostPlatformKeysByDateParamsValidator,
    GetProgrammedSocialPostPlatformKeysByDateQueryDto,
    GetRestaurantInstagramCollaboratorsHistoryParamsDto,
    getRestaurantInstagramCollaboratorsHistoryParamsValidator,
    GetRestaurantUserTagsHistoryParamsDto,
    getRestaurantUserTagsHistoryParamsValidator,
    GetSocialPostByIdDto,
    getSocialPostByIdParamValidator,
    GetSocialPostsByIdsQueryDto,
    getSocialPostsByIdsQueryValidator,
    GetSocialPostsCountsDto,
    GetSocialPostsCountsParamsDto,
    getSocialPostsCountsParamsValidator,
    GetSocialPostsParamsDto,
    getSocialPostsParamsValidator,
    GetSocialPostsQueryDto,
    getSocialPostsQueryValidator,
    PollingPostsStatusesBodyDto,
    pollingPostsStatusesBodyValidator,
    PollingPostStatusDto,
    PostToDuplicateDto,
    ProgrammedSocialPostPlatformKeysByDateDto,
    PublishPostNowParamsDto,
    publishPostNowParamsValidator,
    RefreshSocialPostParamsDto,
    refreshSocialPostParamsValidator,
    SocialPostDto,
    SocialPostItemDto,
    SwapPlannedPublicationDatesBodyDto,
    swapPlannedPublicationDatesBodyValidator,
    TransformToReelParamsDto,
    transformToReelParamsValidator,
    UpdatePlannedPublicationDateBodyDto,
    updatePlannedPublicationDateBodyValidator,
    UpdatePlannedPublicationDateParamsDto,
    updatePlannedPublicationDateParamsValidator,
    updateSocialPostBodyValidator,
    WaitUntilReelThumbnailIsExtractedParamsDto,
    waitUntilReelThumbnailIsExtractedParamsValidator,
    WaitUntilReelThumbnailIsExtractedResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2, IGAccount, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { StoreMetadata } from ':helpers/decorators/store-metadata';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { RequestWithPermissions, RequestWithUser } from ':helpers/utils.types';
import { getUpdateSocialPostMetadata } from ':modules/posts/v2/helpers/get-async-local-storage-metadata.helper';
import { CreateSocialPostUseCase } from ':modules/posts/v2/use-cases/create-social-post/create-social-post.use-case';
import { DeleteSocialPostsUseCase } from ':modules/posts/v2/use-cases/delete-social-posts/delete-social-posts.use-case';
import { DuplicatePostUseCase } from ':modules/posts/v2/use-cases/duplicate-post/duplicate-post.use-case';
import { GetAllPostIdsFromCurrentFilterUseCase } from ':modules/posts/v2/use-cases/get-all-post-ids-from-current-filter/get-all-post-ids-from-current-filter.use-case';
import { GetFeedUseCase } from ':modules/posts/v2/use-cases/get-feed/get-feed.use-case';
import { GetPostsToDuplicateUseCase } from ':modules/posts/v2/use-cases/get-posts-to-duplicate/get-posts-to-duplicate.use-case';
import { GetProgrammedSocialPostPlatformKeysByDateUseCase } from ':modules/posts/v2/use-cases/get-programmed-social-post-platform-keys-by-date/get-programmed-social-post-platform-keys-by-date.use-case';
import { GetRestaurantInstagramCollaboratorsHistoryUseCase } from ':modules/posts/v2/use-cases/get-restaurant-instagram-collaborators-accounts-history/get-restaurant-instagram-collaborators-accounts-history.use-case';
import { GetRestaurantUserTagsHistoryUseCase } from ':modules/posts/v2/use-cases/get-restaurant-user-tags-history/get-restaurant-user-tags-history.use-case';
import { GetSocialPostByIdUseCase } from ':modules/posts/v2/use-cases/get-social-post-by-id/get-social-post-by-id.use-case';
import { GetSocialPostsByIdsUseCase } from ':modules/posts/v2/use-cases/get-social-posts-by-ids/get-social-posts-by-ids.use-case';
import { GetSocialPostsCountsUseCase } from ':modules/posts/v2/use-cases/get-social-posts-counts/get-social-posts-counts.use-case';
import { GetSocialPostsUseCase } from ':modules/posts/v2/use-cases/get-social-posts/get-social-posts.use-case';
import { PollingPostsStatusesUseCase } from ':modules/posts/v2/use-cases/polling-posts-statuses/polling-posts-statuses.use-case';
import { PublishPostNowUseCase } from ':modules/posts/v2/use-cases/publish-post-now/publish-post-now.use-case';
import { RefreshSocialPostUseCase } from ':modules/posts/v2/use-cases/refresh-social-post/refresh-social-post.use-case';
import { SwapPlannedPublicationDatesUseCase } from ':modules/posts/v2/use-cases/swap-planned-publication-dates/swap-planned-publication-dates.use-case';
import { TransformPostToReelUseCase } from ':modules/posts/v2/use-cases/transform-post-to-reel/transform-post-to-reel.use-case';
import { UpdateSocialPostPlannedPublicationDateUseCase } from ':modules/posts/v2/use-cases/update-social-post-planned-publication-date/update-social-post-planned-publication-date.use-case';
import {
    UpdateSocialPostUseCase,
    waitUntilReelThumbnailIsExtracted,
} from ':modules/posts/v2/use-cases/update-social-post/update-social-post.use-case';

import { PostsRepository } from './repository/posts.repository';

@singleton()
export default class PostsController {
    constructor(
        private readonly _createSocialPostUseCase: CreateSocialPostUseCase,
        private readonly _deleteSocialPostsUseCase: DeleteSocialPostsUseCase,
        private readonly _duplicatePostUseCase: DuplicatePostUseCase,
        private readonly _getFeedUseCase: GetFeedUseCase,
        private readonly _getPostsToDuplicateUseCase: GetPostsToDuplicateUseCase,
        private readonly _getProgrammedSocialPostPlatformKeysByDateUseCase: GetProgrammedSocialPostPlatformKeysByDateUseCase,
        private readonly _getRestaurantUserTagsHistoryUseCase: GetRestaurantUserTagsHistoryUseCase,
        private readonly _getRestaurantInstagramCollaboratorsHistoryUseCase: GetRestaurantInstagramCollaboratorsHistoryUseCase,
        private readonly _getSocialPostByIdUseCase: GetSocialPostByIdUseCase,
        private readonly _getSocialPostsByIdsUseCase: GetSocialPostsByIdsUseCase,
        private readonly _getSocialPostsUseCase: GetSocialPostsUseCase,
        private readonly _getSocialPostsCountsUseCase: GetSocialPostsCountsUseCase,
        private readonly _getAllPostIdsFromCurrentFilterUseCase: GetAllPostIdsFromCurrentFilterUseCase,
        private readonly _pollingPostsStatusesUseCase: PollingPostsStatusesUseCase,
        private readonly _publishPostNowUseCase: PublishPostNowUseCase,
        private readonly _refreshSocialPost: RefreshSocialPostUseCase,
        private readonly _swapPlannedPublicationDates: SwapPlannedPublicationDatesUseCase,
        private readonly _transformPostToReelUseCase: TransformPostToReelUseCase,
        private readonly _updateSocialPostUseCase: UpdateSocialPostUseCase,
        private readonly _updateSocialPostPlannedPublicationDateUseCase: UpdateSocialPostPlannedPublicationDateUseCase,
        private readonly _postsRepository: PostsRepository
    ) {}

    @Query(getSocialPostsQueryValidator)
    @Params(getSocialPostsParamsValidator)
    async handleGetSocialPosts(
        req: Request<GetSocialPostsParamsDto, never, never, GetSocialPostsQueryDto>,
        res: Response<ApiResultV2<{ socialPostItems: SocialPostItemDto[]; nextCursor: null | Date }>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { cursor, limit, filter } = req.query;

            const posts = await this._getSocialPostsUseCase.execute(restaurantId, cursor, limit, filter);

            return res.json({ data: posts });
        } catch (err) {
            next(err);
        }
    }

    @Query(getSocialPostsByIdsQueryValidator)
    async handleGetSocialPostsByIds(
        req: Request<never, never, never, GetSocialPostsByIdsQueryDto>,
        res: Response<ApiResultV2<SocialPostItemDto[]>>,
        next: NextFunction
    ) {
        try {
            const { postIds } = req.query;
            const posts = await this._getSocialPostsByIdsUseCase.execute(postIds);
            return res.json({ data: posts });
        } catch (err) {
            next(err);
        }
    }

    @Query(getPostsToDuplicateQueryValidator)
    async handleGetPostsToDuplicate(
        req: Request<never, never, never, GetPostsToDuplicateQueryDto>,
        res: Response<ApiResultV2<PostToDuplicateDto[]>>,
        next: NextFunction
    ) {
        try {
            const { postIds } = req.query;
            const postsToDuplicate = await this._getPostsToDuplicateUseCase.execute(postIds);
            return res.json({ data: postsToDuplicate });
        } catch (err) {
            next(err);
        }
    }

    @Params(getSocialPostsCountsParamsValidator)
    async handleGetSocialPostsCounts(
        req: Request<GetSocialPostsCountsParamsDto>,
        res: Response<ApiResultV2<GetSocialPostsCountsDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const socialPostsCounts = await this._getSocialPostsCountsUseCase.execute(restaurantId);

            return res.json({ data: socialPostsCounts });
        } catch (err) {
            next(err);
        }
    }

    @Query(getFeedQueryValidator)
    @Params(getFeedParamsValidator)
    async handleGetFeed(
        req: Request<GetFeedParamsDto, never, never, GetFeedQueryDto>,
        res: Response<ApiResultV2<{ feed: FeedItemDto[]; nextCursor: null | Date }>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { cursor, limit } = req.query;

            const feed = await this._getFeedUseCase.execute(restaurantId, cursor, limit);

            return res.json({ data: feed });
        } catch (err) {
            next(err);
        }
    }

    @Params(getProgrammedSocialPostPlatformKeysByDateParamsValidator)
    async handleGetProgrammedSocialPostPlatformKeysByDate(
        req: Request<GetProgrammedSocialPostPlatformKeysByDateParamsDto, never, never, GetProgrammedSocialPostPlatformKeysByDateQueryDto>,
        res: Response<ApiResultV2<ProgrammedSocialPostPlatformKeysByDateDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { timezone } = req.query;

            const result = await this._getProgrammedSocialPostPlatformKeysByDateUseCase.execute(restaurantId, timezone);

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Query(getAllPostIdsFromCurrentFilterQueryValidator)
    @Params(getAllPostIdsFromCurrentFilterParamsValidator)
    async handleGetAllPostIdsFromCurrentFilter(
        req: Request<GetAllPostIdsFromCurrentFilterParamsDto, never, never, GetAllPostIdsFromCurrentFilterQueryDto>,
        res: Response<ApiResultV2<string[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { filter } = req.query;

            const postIds = await this._getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, filter);

            return res.json({ data: postIds });
        } catch (err) {
            next(err);
        }
    }

    @Body(pollingPostsStatusesBodyValidator)
    async handlePollingPostsStatuses(
        req: Request<never, never, PollingPostsStatusesBodyDto>,
        res: Response<ApiResultV2<PollingPostStatusDto[]>>,
        next: NextFunction
    ) {
        try {
            const { bindingIds } = req.body;
            const result = await this._pollingPostsStatusesUseCase.execute(bindingIds);
            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(createSocialPostBodyValidator)
    async handleCreateSocialPost(req: RequestWithUser<any, any, CreateSocialPostDto>, res: Response, next: NextFunction) {
        try {
            const { user } = req;
            assert(user, 'User not found');
            const { restaurantId, date, isReel } = req.body;

            assert(user, 'Missing user');

            const newPost = await this._createSocialPostUseCase.execute({
                restaurantId,
                author: {
                    id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                },
                date,
                isReel,
            });

            return res.json({ msg: 'Success', data: newPost });
        } catch (err) {
            next(err);
        }
    }

    @Body(updateSocialPostBodyValidator)
    @StoreMetadata(getUpdateSocialPostMetadata)
    async handleUpdateSocialPost(
        req: RequestWithUser<any, any, SocialPostDto>,
        res: Response<ApiResultV2<SocialPostDto>>,
        next: NextFunction
    ) {
        try {
            const { user } = req;
            assert(user, 'User not found');

            assert(user, 'Missing user');

            const updatePost = await this._updateSocialPostUseCase.execute({
                post: req.body,
                author: {
                    id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                },
            });

            return res.json({ data: updatePost });
        } catch (err) {
            next(err);
        }
    }

    @Params(getSocialPostByIdParamValidator)
    async handleGetPostById(req: RequestWithUser<GetSocialPostByIdDto>, res: Response<ApiResultV2<SocialPostDto>>, next: NextFunction) {
        try {
            const { postId } = req.params;

            const post = await this._getSocialPostByIdUseCase.execute({
                id: postId,
            });

            return res.json({ data: post });
        } catch (err) {
            next(err);
        }
    }

    @Body(deleteSocialPostsBodyValidator)
    async handleDeleteSocialPosts(
        req: Request<never, never, DeleteSocialPostsBodyDto>,
        res: Response<ApiResultV2<{ postId: string; success: boolean }[]>>,
        next: NextFunction
    ) {
        try {
            const { postIds } = req.body;

            const result = await this._deleteSocialPostsUseCase.execute(postIds);

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(updatePlannedPublicationDateParamsValidator)
    @Body(updatePlannedPublicationDateBodyValidator)
    async handleUpdateSocialPostPlannedPublicationDate(
        req: RequestWithPermissions<UpdatePlannedPublicationDateParamsDto, never, never, UpdatePlannedPublicationDateBodyDto>,
        res: Response<ApiResultV2<SocialPostDto>>,
        next: NextFunction
    ) {
        try {
            assert(req.user, 'User not found');
            const { postId } = req.params;
            const { plannedPublicationDate } = req.body;

            assert(req.user, 'Missing user');

            const author = {
                id: req.user._id.toString(),
                name: req.user.name,
                lastname: req.user.lastname,
            };

            const post = await this._updateSocialPostPlannedPublicationDateUseCase.execute(postId, plannedPublicationDate, author);

            return res.json({ data: post });
        } catch (err) {
            next(err);
        }
    }

    @Params(transformToReelParamsValidator)
    async handleTransformPostToReel(
        req: RequestWithPermissions<TransformToReelParamsDto>,
        res: Response<ApiResultV2<SocialPostDto>>,
        next: NextFunction
    ) {
        try {
            assert(req.user, 'User not found');
            const { postId } = req.params;
            assert(req.user, 'Missing user');
            const author = {
                id: req.user._id.toString(),
                name: req.user.name,
                lastname: req.user.lastname,
            };

            const post = await this._transformPostToReelUseCase.execute({ postId, author });

            return res.json({ data: post });
        } catch (err) {
            next(err);
        }
    }

    @Body(swapPlannedPublicationDatesBodyValidator)
    async handleSwapPlannedPublicationDates(
        req: RequestWithPermissions<never, never, SwapPlannedPublicationDatesBodyDto>,
        res: Response<ApiResultV2<void>>,
        next: NextFunction
    ) {
        try {
            assert(req.userRestaurantsAbility, 'User restaurants ability not found');
            assert(req.user, 'User not found');
            await this._swapPlannedPublicationDates.execute(req.user._id.toString(), req.userRestaurantsAbility, req.body);
            return res.status(204).end();
        } catch (err) {
            next(err);
        }
    }

    @Params(refreshSocialPostParamsValidator)
    async handleRefreshSocialPost(
        req: RequestWithUser<RefreshSocialPostParamsDto>,
        res: Response<ApiResultV2<SocialPostItemDto>>,
        next: NextFunction
    ) {
        try {
            const { postId } = req.params;

            const post = await this._refreshSocialPost.execute({
                id: postId,
            });

            return res.json({ data: post });
        } catch (err) {
            next(err);
        }
    }

    @Params(publishPostNowParamsValidator)
    async handlePublishPostNow(req: RequestWithUser<PublishPostNowParamsDto>, res: Response, next: NextFunction) {
        try {
            const { postId } = req.params;
            assert(req.user, 'User not found');

            await this._publishPostNowUseCase.execute(postId, req.user._id.toString());

            return res.status(204).end();
        } catch (err) {
            next(err);
        }
    }

    @Params(getRestaurantUserTagsHistoryParamsValidator)
    async handleGetRestaurantUserTagsHistory(
        req: Request<GetRestaurantUserTagsHistoryParamsDto>,
        res: Response<ApiResultV2<{ username: string; count: number; igAccount: IGAccount }[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const result = await this._getRestaurantUserTagsHistoryUseCase.execute(restaurantId);

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(getRestaurantInstagramCollaboratorsHistoryParamsValidator)
    async handleGetRestaurantInstagramCollaboratorsHistory(
        req: Request<GetRestaurantInstagramCollaboratorsHistoryParamsDto>,
        res: Response<ApiResultV2<{ username: string; count: number; igAccount: IGAccount }[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const result = await this._getRestaurantInstagramCollaboratorsHistoryUseCase.execute(restaurantId);

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(duplicatePostV2ParamsValidator)
    @Body(duplicatePostV2BodyValidator)
    async handleDuplicatePost(
        req: RequestWithPermissions<DuplicatePostV2ParamsDto, never, DuplicatePostV2BodyDto>,
        res: Response<ApiResultV2<DuplicatePostV2ResponseDto>>,
        next: NextFunction
    ) {
        try {
            assert(req.user, 'Missing user');
            const { restaurantId } = req.params;
            const { restaurantIds, postRefsToDuplicate, postDestination, customFields } = req.body;

            const userRestaurantsAbility = req.userRestaurantsAbility;
            assert(userRestaurantsAbility, 'User restaurants ability not found');
            assert(req.user, 'User not found');

            const result = await this._duplicatePostUseCase.execute({
                restaurantIds,
                postRefsToDuplicate,
                author: {
                    id: req.user._id.toString(),
                    name: req.user.name,
                    lastname: req.user.lastname,
                },
                userRestaurantsAbility,
                fromRestaurantId: restaurantId,
                postDestination,
                customFields,
            });

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(waitUntilReelThumbnailIsExtractedParamsValidator)
    async waitUntilReelThumbnailIsExtracted(
        req: RequestWithPermissions<WaitUntilReelThumbnailIsExtractedParamsDto, never, never>,
        res: Response<ApiResultV2<WaitUntilReelThumbnailIsExtractedResponseDto>>,
        next: NextFunction
    ) {
        try {
            await waitUntilReelThumbnailIsExtracted(req.params.postId);
            const post = await this._postsRepository.findById(req.params.postId);
            if (!post) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {});
            }
            return res.json({ data: { reelThumbnailId: post.thumbnail?.toString() ?? null } });
        } catch (err) {
            next(err);
        }
    }
}
