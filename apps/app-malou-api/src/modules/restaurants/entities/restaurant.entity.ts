import { RestaurantResponseDto, RestaurantWithoutStickerDto } from '@malou-io/package-dto';
import { IMedia, IRestaurant } from '@malou-io/package-models';
import {
    BusinessCategory,
    getNumberOfMonthsSinceMostRecentDate,
    isNotNil,
    RestaurantCalendarEventsCountryType,
} from '@malou-io/package-utils';

import { Media } from ':modules/media/entities/media.entity';
import { Access } from ':modules/restaurants/entities/access.entity';
import { Address } from ':modules/restaurants/entities/address.entity';
import { BookmarkedPost } from ':modules/restaurants/entities/bookmarked-post.entity';
import { CurrentState } from ':modules/restaurants/entities/current-state.entity';
import { Description } from ':modules/restaurants/entities/description.entity';

export class Restaurant {
    _id: string;
    access: Access[];
    active: boolean;
    address?: Address;
    ai: IRestaurant['ai'];
    availableHoursTypeIds: string[];
    bookmarkedPosts: BookmarkedPost[];
    boosterPack?: IRestaurant['boosterPack'];
    bricks: string[];
    bricksPostalCode?: string;
    calendarEvents: string[];
    calendarEventsCountry?: RestaurantCalendarEventsCountryType;
    category?: string;
    categoryList: string[];
    commentsLastUpdate?: Date;
    cover?: string;
    coverChanged: boolean;
    coverPopulated?: Media;
    currentState?: CurrentState;
    descriptions: Description[];
    email?: string;
    internalName?: string;
    isClaimed?: boolean;
    isClosedTemporarily: boolean;
    latlng?: IRestaurant['latlng'];
    logo?: string;
    logoChanged: boolean;
    logoPopulated?: Media;
    menuUrl?: string;
    name: string;
    openingDate?: Date;
    organizationId: string;
    otherHours?: IRestaurant['otherHours'];
    phone?: IRestaurant['phone'];
    placeId: string;
    regularHours?: IRestaurant['regularHours'];
    relatedUrls: string[];
    reviewsLastUpdate?: Date;
    specialHours: IRestaurant['specialHours'];
    socialId?: string;
    providerId?: string;
    type: BusinessCategory;
    toolboxMenuUrl?: string;
    uniqueKey: string;
    website?: string;
    totemDisplayName?: {
        title?: string;
        text?: string;
    };
    socialNetworkUrls?: IRestaurant['socialNetworkUrls'];
    orderUrl?: string;
    reservationUrl?: string;
    brandKeywords?: {
        brandNameKeywords: string[];
        brandGroupKeywords: string[];
    };
    createdAt: Date;
    updatedAt: Date;

    constructor(data: IRestaurant & { logoPopulated?: IMedia; coverPopulated?: IMedia }) {
        this._id = data._id.toString();
        this.access = data.access.map((access) => new Access(access));
        this.active = data.active;
        this.address = data.address ? new Address(data.address) : undefined;
        this.availableHoursTypeIds = data.availableHoursTypeIds?.map((availableHoursTypeId) => availableHoursTypeId.toString()) ?? [];
        this.ai = data.ai;
        this.bookmarkedPosts = data.bookmarkedPosts.map((bookmarkedPost) => new BookmarkedPost(bookmarkedPost));
        this.boosterPack = data.boosterPack;
        this.bricks = data.bricks.map((brick) => brick.toString());
        this.bricksPostalCode = data.bricksPostalCode;
        this.calendarEvents = data.calendarEvents.map((calendarEvent) => {
            return calendarEvent.toString();
        });
        this.calendarEventsCountry = data.calendarEventsCountry;
        this.category = data.category?.toString();
        this.categoryList = data.categoryList.map((id) => id.toString());
        this.commentsLastUpdate = data.commentsLastUpdate;
        this.cover = data.cover?.toString();
        this.coverChanged = data.coverChanged;
        this.coverPopulated = data.coverPopulated ? this._IMediaToMedia(data.coverPopulated) : undefined;
        this.currentState = data.currentState ? new CurrentState(data.currentState) : undefined;
        this.descriptions = data.descriptions.map((description) => new Description(description));
        this.email = data.email ?? undefined;
        this.internalName = data.internalName;
        this.isClaimed = data.isClaimed;
        this.isClosedTemporarily = data.isClosedTemporarily;
        this.latlng = data.latlng;
        this.logo = data.logo?.toString();
        this.logoChanged = data.logoChanged;
        this.logoPopulated = data.logoPopulated ? this._IMediaToMedia(data.logoPopulated) : undefined;
        this.menuUrl = data.menuUrl ?? undefined;
        this.name = data.name;
        this.openingDate = data.openingDate;
        this.organizationId = data.organizationId.toString();
        this.otherHours = data.otherHours;
        this.phone = data.phone;
        this.placeId = data.placeId;
        this.regularHours = data.regularHours;
        this.relatedUrls = data.relatedUrls;
        this.reviewsLastUpdate = data.reviewsLastUpdate;
        this.specialHours = data.specialHours;
        this.socialId = data.socialId;
        this.type = data.type;
        this.toolboxMenuUrl = data.toolboxMenuUrl;
        this.uniqueKey = data.uniqueKey;
        this.website = data.website ?? undefined;
        this.totemDisplayName = data.totemDisplayName;
        this.createdAt = data.createdAt;
        this.updatedAt = data.updatedAt;
        this.socialNetworkUrls = data.socialNetworkUrls;
        this.orderUrl = data.orderUrl ?? undefined;
        this.reservationUrl = data.reservationUrl ?? undefined;
        this.brandKeywords = data.brandKeywords ?? undefined;
    }

    toDto(): RestaurantResponseDto {
        return {
            _id: this._id.toString(),
            id: this._id.toString(),
            access: this.access.map((access) => access.toDto()),
            active: this.active,
            address: this.address?.toDto(),
            ai: this.ai,
            availableHoursTypeIds: this.availableHoursTypeIds,
            bookmarkedPosts: this.bookmarkedPosts.map((bookmarkedPost) => bookmarkedPost.toDto()),
            boosterPack: { activated: this.boosterPack?.activated, activationDate: this.boosterPack?.activationDate ?? null },
            bricks: this.bricks,
            bricksPostalCode: this.bricksPostalCode,
            calendarEvents: this.calendarEvents.map((calendarEvent) => calendarEvent.toString()),
            calendarEventsCountry: this.calendarEventsCountry,
            category: this.category,
            categoryList: this.categoryList,
            commentsLastUpdate: this.commentsLastUpdate?.toISOString(),
            cover: this.cover,
            coverChanged: this.coverChanged,
            coverPopulated: this.coverPopulated?.toDto(),
            currentState: this.currentState?.toDto(),
            descriptions: this.descriptions.map((description) => description.toDto()),
            email: this.email,
            internalName: this.internalName,
            isClaimed: this.isClaimed,
            isClosedTemporarily: this.isClosedTemporarily,
            latlng: this.latlng,
            logo: this.logo,
            logoChanged: this.logoChanged,
            logoPopulated: this.logoPopulated?.toDto(),
            menuUrl: this.menuUrl,
            name: this.name,
            openingDate: this.openingDate?.toISOString(),
            organizationId: this.organizationId,
            otherHours: this.otherHours,
            phone: this.phone ? { prefix: this.phone.prefix, digits: this.phone.digits } : undefined,
            placeId: this.placeId,
            regularHours: this.regularHours ?? undefined,
            relatedUrls: this.relatedUrls,
            reviewsLastUpdate: this.reviewsLastUpdate?.toISOString(),
            socialId: this.socialId,
            specialHours: this.specialHours,
            toolboxMenuUrl: this.toolboxMenuUrl,
            type: this.type,
            uniqueKey: this.uniqueKey,
            website: this.website,
            totemDisplayName: this.totemDisplayName,
            socialNetworkUrls: this.socialNetworkUrls?.filter(isNotNil) ?? [],
            orderUrl: this.orderUrl,
            reservationUrl: this.reservationUrl,
            createdAt: this.createdAt.toISOString(),
            updatedAt: this.updatedAt.toISOString(),
        };
    }

    toRestaurantWithoutStickerDto(): RestaurantWithoutStickerDto {
        return {
            _id: this._id.toString(),
            id: this._id.toString(),
            address: this.address?.toDto(),
            name: this.name,
            internalName: this.internalName,
            type: this.type,
        };
    }

    isCreatedAfterDate(date: Date): boolean {
        return this.createdAt?.getTime() >= date.getTime();
    }

    getNumberOfMonthSinceCreatedAndOpened(): number {
        return getNumberOfMonthsSinceMostRecentDate([this.createdAt, this.openingDate].filter(isNotNil));
    }

    private _IMediaToMedia(media: IMedia): Media {
        return new Media({
            ...media,
            id: media._id.toString(),
            duplicatedFromRestaurantId: media.duplicatedFromRestaurantId?.toString(),
            folderId: media.folderId?.toString(),
            originalMediaId: media.originalMediaId?.toString(),
            postIds: media.postIds?.map((postId) => postId.toString()),
            restaurantId: media.restaurantId?.toString(),
            userId: media.userId?.toString(),
        });
    }
}
