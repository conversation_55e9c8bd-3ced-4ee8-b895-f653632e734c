import assert from 'node:assert';
import { injectable } from 'tsyringe';

import {
    GetRestaurantsFromProviderQueryDto,
    GetRestaurantsFromProviderResponseDto,
    HyperlineLocationDto,
    PlatformSearchWithConnectabilityDto,
} from '@malou-io/package-dto';
import { ConnectableStatus, MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import { SearchSocialIdsUseCase } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.use-case';
import { HyperlineProvider } from ':providers/hyperline/hyperline.provider';

@injectable()
export class GetRestaurantsFromProviderUseCase {
    constructor(
        private readonly _searchSocialIdsUseCase: SearchSocialIdsUseCase,
        private readonly _hyperlineProvider: HyperlineProvider,
        private readonly _organizationsRepository: OrganizationsRepository
    ) {}

    async execute(query: GetRestaurantsFromProviderQueryDto): Promise<GetRestaurantsFromProviderResponseDto> {
        const { organizationId, platformKey = PlatformKey.GMB, credentialId } = query;

        try {
            const organization = await this._organizationsRepository.findOneOrFail({
                filter: { _id: organizationId },
                options: { lean: true },
            });

            assert(organization.providerId, 'Missing organization providerId');

            const [platformData, hyperlineData] = await Promise.all([
                this._searchSocialIdsUseCase.execute(platformKey, {
                    restaurantId: null,
                    credentialId,
                }),
                this._hyperlineProvider.getLocationsByOrganization({
                    organizationProviderId: organization.providerId,
                }),
            ]);

            const hyperlineLocationMap = new Map<string, HyperlineLocationDto>();
            for (const location of hyperlineData.locations) {
                if (location.placeId) {
                    hyperlineLocationMap.set(location.placeId, {
                        id: location.id,
                        name: location.name,
                        placeId: location.placeId,
                        malouRestaurantId: location.malouRestaurantId,
                        isBrandAccount: location.isBrandAccount,
                    });
                }
            }

            let mergedResults: PlatformSearchWithConnectabilityDto[] = [];
            switch (platformKey) {
                case PlatformKey.GMB:
                    mergedResults = this._getGmbMergedResults(platformData.list, hyperlineData.locations);
                    break;
                case PlatformKey.FACEBOOK:
                    mergedResults = this._getFacebookMergedResults(platformData.list, hyperlineData.locations);
                    break;
                default:
                    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                        message: 'Platform not implemented',
                        metadata: { platformKey },
                    });
            }

            logger.info('[GET_RESTAURANTS_FROM_PROVIDER] Successfully merged platform and Hyperline data', {
                organizationId,
                platformKey,
                platformResultsCount: platformData.list.length,
                hyperlineLocationsCount: hyperlineData.locations.length,
                mergedResultsCount: mergedResults.length,
            });

            mergedResults.sort((a, b) => {
                if (a.connectableStatus === b.connectableStatus) {
                    return a.name.localeCompare(b.name);
                }
                return a.connectableStatus === ConnectableStatus.CONNECTABLE ? -1 : 1;
            });

            return {
                list: mergedResults,
                hyperlineLocations: Array.from(hyperlineLocationMap.values()),
            };
        } catch (error: any) {
            logger.error('[GET_RESTAURANTS_FROM_PROVIDER] Error fetching restaurants from provider', {
                organizationId,
                platformKey,
                error: error.message,
            });

            throw error;
        }
    }

    private _getGmbMergedResults(
        platformData: MalouRestaurantSearchResult[],
        hyperlineData: HyperlineLocationDto[]
    ): PlatformSearchWithConnectabilityDto[] {
        return platformData.map((platformResult) => {
            const socialId = platformResult.socialId;
            const hyperlineLocation = socialId ? hyperlineData.find((location) => location.placeId === socialId) : undefined;

            let connectableStatus: ConnectableStatus = ConnectableStatus.NOT_CONNECTABLE;

            if (hyperlineLocation) {
                connectableStatus = hyperlineLocation.malouRestaurantId
                    ? ConnectableStatus.ALREADY_CONNECTED
                    : hyperlineLocation.placeId
                      ? ConnectableStatus.CONNECTABLE
                      : ConnectableStatus.NOT_CONNECTABLE;
            }

            return {
                name: platformResult.name || '',
                locationId: platformResult.locationId || platformResult.socialId,
                formattedAddress: platformResult.formattedAddress || '',
                picture: platformResult.picture || '',
                rating: platformResult.rating || 0,
                socialUrl: platformResult.socialUrl || '',
                socialId,
                connectableStatus,
                hyperlineLocationId: hyperlineLocation?.id,
                access: platformResult.access,
                accountId: platformResult.accountId,
                accountName: platformResult.accountName,
                apiEndpointV2: platformResult.apiEndpointV2,
                pageCategory: platformResult.pageCategory,
                parentSocialId: platformResult.parentSocialId,
                hasTransitionedToNewPageExperience: platformResult.hasTransitionedToNewPageExperience,
                username: platformResult.username,
                drnId: platformResult.drnId,
            };
        });
    }

    private _getFacebookMergedResults(
        platformData: MalouRestaurantSearchResult[],
        hyperlineData: HyperlineLocationDto[]
    ): PlatformSearchWithConnectabilityDto[] {
        const availableBrandAccounts = hyperlineData.filter((location) => location.isBrandAccount && !location.malouRestaurantId);
        const connectableStatus = availableBrandAccounts.length > 0 ? ConnectableStatus.CONNECTABLE : ConnectableStatus.NOT_CONNECTABLE;

        return platformData.map((platformResult) => {
            return {
                name: platformResult.name || '',
                locationId: platformResult.locationId || platformResult.socialId,
                formattedAddress: platformResult.formattedAddress || '',
                picture: platformResult.picture || '',
                rating: platformResult.rating || 0,
                socialUrl: platformResult.socialUrl || '',
                socialId: platformResult.socialId,
                connectableStatus,
                hyperlineLocationId: availableBrandAccounts[0]?.id,
                access: platformResult.access,
                accountId: platformResult.accountId,
                accountName: platformResult.accountName,
                apiEndpointV2: platformResult.apiEndpointV2,
                pageCategory: platformResult.pageCategory,
                parentSocialId: platformResult.parentSocialId,
                hasTransitionedToNewPageExperience: platformResult.hasTransitionedToNewPageExperience,
                username: platformResult.username,
                drnId: platformResult.drnId,
            };
        });
    }
}
