import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import { HyperlineProvider } from ':providers/hyperline/hyperline.provider';

import RestaurantsRepository from '../restaurants.repository';

export interface UpdateHyperlineCustomerRequest {
    restaurantId: string;
    hyperlineCustomerId: string;
    restaurantName?: string;
}

export interface UpdateHyperlineCustomerResponse {
    success: boolean;
    restaurantId: string;
    hyperlineCustomerId: string;
}

@singleton()
export class UpdateHyperlineCustomerUseCase {
    constructor(
        private readonly _hyperlineProvider: HyperlineProvider,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(request: UpdateHyperlineCustomerRequest): Promise<UpdateHyperlineCustomerResponse> {
        const { restaurantId, hyperlineCustomerId, restaurantName } = request;

        logger.info('[UPDATE_HYPERLINE_CUSTOMER] Starting Hyperline customer update', {
            restaurantId,
            hyperlineCustomerId,
        });

        try {
            // Step 1: Update Hyperline customer with restaurant ID as external_id
            const updateData = {
                customerId: hyperlineCustomerId,
                external_id: restaurantId,
                ...(restaurantName && { name: restaurantName }),
            };

            const hyperlineResponse = await this._hyperlineProvider.updateCustomer(updateData);
            
            logger.info('[UPDATE_HYPERLINE_CUSTOMER] Hyperline customer updated successfully', {
                restaurantId,
                hyperlineCustomerId,
                hyperlineResponse: hyperlineResponse.id,
            });

            // Step 2: Update restaurant with providerId (Hyperline customer ID)
            const updatedRestaurant = await this._restaurantsRepository.findOneAndUpdate({
                filter: { _id: restaurantId },
                update: { providerId: hyperlineCustomerId },
                options: { new: true },
            });

            if (!updatedRestaurant) {
                // If restaurant update fails, we should rollback the Hyperline update
                logger.error('[UPDATE_HYPERLINE_CUSTOMER] Restaurant not found for providerId update, attempting rollback', {
                    restaurantId,
                    hyperlineCustomerId,
                });
                
                await this._rollbackHyperlineUpdate(hyperlineCustomerId);
                throw new Error(`Restaurant with ID ${restaurantId} not found`);
            }

            logger.info('[UPDATE_HYPERLINE_CUSTOMER] Restaurant providerId updated successfully', {
                restaurantId,
                hyperlineCustomerId,
                providerId: updatedRestaurant.providerId,
            });

            return {
                success: true,
                restaurantId,
                hyperlineCustomerId,
            };
        } catch (error: any) {
            logger.error('[UPDATE_HYPERLINE_CUSTOMER] Error during Hyperline integration', {
                restaurantId,
                hyperlineCustomerId,
                error: error.message,
            });

            // If we get here and the error is not from Hyperline API call,
            // it means Hyperline was updated but restaurant update failed
            if (error.message.includes('Restaurant with ID')) {
                // Rollback already attempted in the catch block above
                throw error;
            }

            // If Hyperline API call failed, no rollback needed
            throw new Error(`Failed to update Hyperline customer: ${error.message}`);
        }
    }

    private async _rollbackHyperlineUpdate(hyperlineCustomerId: string): Promise<void> {
        try {
            logger.info('[UPDATE_HYPERLINE_CUSTOMER] Attempting to rollback Hyperline customer update', {
                hyperlineCustomerId,
            });

            // Reset external_id to null to rollback the change
            await this._hyperlineProvider.updateCustomer({
                customerId: hyperlineCustomerId,
                external_id: null,
            });

            logger.info('[UPDATE_HYPERLINE_CUSTOMER] Hyperline customer rollback successful', {
                hyperlineCustomerId,
            });
        } catch (rollbackError: any) {
            logger.error('[UPDATE_HYPERLINE_CUSTOMER] Failed to rollback Hyperline customer update', {
                hyperlineCustomerId,
                rollbackError: rollbackError.message,
            });
            // Don't throw here as we want to preserve the original error
        }
    }
}
