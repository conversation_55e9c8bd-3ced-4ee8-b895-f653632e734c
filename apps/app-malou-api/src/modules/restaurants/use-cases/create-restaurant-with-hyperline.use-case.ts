import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import { HyperlineProvider } from ':providers/hyperline/hyperline.provider';

import RestaurantsRepository from '../restaurants.repository';
import RestaurantsUseCases from '../restaurants.use-cases';
import { UpdateHyperlineCustomerUseCase } from './update-hyperline-customer.use-case';

export interface CreateRestaurantWithHyperlineRequest {
    restaurantId: string;
    hyperlineCustomerId: string;
    restaurantName?: string;
}

export interface CreateRestaurantWithHyperlineResponse {
    success: boolean;
    restaurantId: string;
    hyperlineCustomerId: string;
}

@singleton()
export class CreateRestaurantWithHyperlineUseCase {
    constructor(
        private readonly _updateHyperlineCustomerUseCase: UpdateHyperlineCustomerUseCase,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _restaurantsUseCases: RestaurantsUseCases,
        private readonly _hyperlineProvider: HyperlineProvider
    ) {}

    async execute(request: CreateRestaurantWithHyperlineRequest): Promise<CreateRestaurantWithHyperlineResponse> {
        const { restaurantId, hyperlineCustomerId, restaurantName } = request;

        logger.info('[CREATE_RESTAURANT_WITH_HYPERLINE] Starting restaurant-Hyperline integration', {
            restaurantId,
            hyperlineCustomerId,
        });

        try {
            // Attempt to update Hyperline customer and restaurant providerId
            const result = await this._updateHyperlineCustomerUseCase.execute({
                restaurantId,
                hyperlineCustomerId,
                restaurantName,
            });

            logger.info('[CREATE_RESTAURANT_WITH_HYPERLINE] Restaurant-Hyperline integration completed successfully', {
                restaurantId,
                hyperlineCustomerId,
            });

            return result;
        } catch (error: any) {
            logger.error('[CREATE_RESTAURANT_WITH_HYPERLINE] Integration failed, initiating complete rollback', {
                restaurantId,
                hyperlineCustomerId,
                error: error.message,
            });

            // If Hyperline integration fails, we need to delete the restaurant entirely
            await this._rollbackRestaurantCreation(restaurantId);
            
            throw new Error(`Restaurant creation failed due to Hyperline integration error: ${error.message}`);
        }
    }

    private async _rollbackRestaurantCreation(restaurantId: string): Promise<void> {
        try {
            logger.info('[CREATE_RESTAURANT_WITH_HYPERLINE] Starting restaurant rollback', {
                restaurantId,
            });

            // Get the restaurant to check if it exists
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: restaurantId },
            });

            if (!restaurant) {
                logger.warn('[CREATE_RESTAURANT_WITH_HYPERLINE] Restaurant not found for rollback', {
                    restaurantId,
                });
                return;
            }

            // Delete the restaurant
            await this._restaurantsRepository.deleteOne({
                filter: { _id: restaurantId },
            });

            logger.info('[CREATE_RESTAURANT_WITH_HYPERLINE] Restaurant rollback completed successfully', {
                restaurantId,
            });
        } catch (rollbackError: any) {
            logger.error('[CREATE_RESTAURANT_WITH_HYPERLINE] Failed to rollback restaurant creation', {
                restaurantId,
                rollbackError: rollbackError.message,
            });
            // Don't throw here as we want to preserve the original error
        }
    }
}
