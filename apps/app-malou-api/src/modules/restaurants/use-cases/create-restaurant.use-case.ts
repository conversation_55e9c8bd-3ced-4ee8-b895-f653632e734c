import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { CreateRestaurantBodyDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { BusinessCategory, CaslRole, MalouErrorCode, PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import LabelsUseCases from ':modules/labels/labels.use-cases';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import { KeywordSearchImpressionsService } from ':modules/platforms/services/keyword-search-impressions/keyword-search-impressions.service';
import ReportsRepository from ':modules/reports/reports.repository';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import { RestaurantsDtoMapper } from ':modules/restaurants/mappers/restaurants.dto-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import RestaurantsUseCases from ':modules/restaurants/restaurants.use-cases';
import YextActivateLocationUseCase from ':modules/restaurants/use-cases/activate-yext-location.use-case';
import AddDefaultReviewAutomationsUseCase from ':modules/restaurants/use-cases/add-default-review-automations.use-case';
import UsersUseCases from ':modules/users/users.use-cases';

@singleton()
export class CreateRestaurantUseCase {
    constructor(
        // todo refacto because a usecase cant import another usecase
        private readonly _platformsUseCases: PlatformsUseCases,
        // todo refacto because a usecase cant import another usecase
        private readonly _restaurantsUseCases: RestaurantsUseCases,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        // todo refacto because a usecase cant import another usecase
        private readonly _labelsUseCases: LabelsUseCases,
        // todo refacto because a usecase cant import another usecase
        private readonly _usersUseCases: UsersUseCases,
        private readonly _reportsRepository: ReportsRepository,
        private readonly _restaurantAiSettingRepository: RestaurantAiSettingsRepository,
        // todo refacto because a usecase cant import another usecase
        private readonly _yextActivateLocationUseCase: YextActivateLocationUseCase,
        private readonly _addDefaultReviewAutomationsUseCase: AddDefaultReviewAutomationsUseCase,
        private readonly _restaurantsMapper: RestaurantsDtoMapper,
        private readonly _platformKeywordSearchImpressionsService: KeywordSearchImpressionsService,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _createRestaurantWithHyperlineUseCase: CreateRestaurantWithHyperlineUseCase
    ) {}

    // TODO: Need to separate this use case into two different use cases - one for GMB and one for Facebook
    async execute(payload: CreateRestaurantBodyDto & { userId: string }) {
        const {
            socialId,
            type,
            credentialId,
            organizationId: organizationInputId,
            fromForm,
            accountId,
            apiEndpointV2,
            fromTests,
            hyperlineLocationId,
        } = payload;
        const userId = payload.userId;
        const organizationId = organizationInputId;

        const userCanCreateRestaurant = await this._restaurantsUseCases.userCanCreate({
            userId,
            placeId: socialId,
            socialId,
            organizationId,
        });

        if (!userCanCreateRestaurant) {
            throw new MalouError(MalouErrorCode.ABOVE_RESTAURANTS_LIMITS, {
                message: 'Cannot create more restaurants, max limit reached',
            });
        }

        const platformKey = type === BusinessCategory.BRAND ? PlatformKey.FACEBOOK : PlatformKey.GMB;

        let fullPlatformData = payload;
        if (!fromForm && !fromTests) {
            logger.info('[CREATE_RESTAURANT] Getting platform data', { credentialId, socialId, platformKey });
            fullPlatformData = await this._platformsUseCases.getLocationDataForPlatform({
                platformKey,
                credentialId: toDbId(credentialId),
                socialId,
                apiEndpointV2,
            });
        }

        Object.assign(fullPlatformData, { type, placeId: socialId, organizationId });
        // We don't have a restaurant yet
        const restaurantId = null;
        logger.info('[CREATE_RESTAURANT] Upserting restaurant', { restaurant: fullPlatformData });
        const upsertedRestaurant = await this._restaurantsUseCases.upsertRestaurant(fullPlatformData, restaurantId);
        await this._restaurantsUseCases.addRestaurantForUser(userId, upsertedRestaurant._id.toString(), CaslRole.OWNER);
        await this._restaurantsUseCases.addRestaurantForAppAdmin(upsertedRestaurant._id.toString());
        if (upsertedRestaurant.type === BusinessCategory.LOCAL_BUSINESS) {
            await this._addDefaultReviewAutomationsUseCase.execute(upsertedRestaurant._id.toString());
        }

        await this._restaurantsRepository.findOneAndUpdate({ filter: { _id: upsertedRestaurant._id }, update: { organizationId } });

        if (!fromForm) {
            // some accountIds are in a bad format in the db
            const formattedAccountId = accountId && (accountId.includes('accounts') ? '' : 'accounts/') + accountId;
            logger.info('[CREATE_RESTAURANT] Initializing platform', { credentialId, socialId, platformKey, formattedAccountId });
            const initializedPlatform = await this._platformsUseCases.initializePlatform(
                platformKey,
                upsertedRestaurant._id.toString(),
                apiEndpointV2,
                credentialId,
                formattedAccountId
            );

            // If location is verified and a rating is effectively returned, add it to the platform insights
            if (initializedPlatform.rating) {
                const today = DateTime.local();
                const { year, month, day } = today;
                const { rating, socialId: platformSocialId, key } = initializedPlatform;

                const filter = {
                    platformKey: key,
                    socialId: platformSocialId ?? undefined,
                    metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                    year,
                    month: month - 1,
                    day,
                };

                const insight = {
                    ...filter,
                    value: rating,
                    date: today.toJSDate(),
                };
                await this._platformInsightsRepository.upsert({ filter, update: insight });
            }

            // Create a message queue to fetch monthly keyword search impressions for the platforms that support it
            if (initializedPlatform._id) {
                this._platformKeywordSearchImpressionsService
                    .createMessageQueueToFetchMonthlyKeywordSearchImpressions(initializedPlatform._id.toString(), platformKey)
                    .catch((err) =>
                        logger.error('[CreateRestaurantUseCase] - createMessageQueueToFetchMonthlyKeywordSearchImpressions error', err)
                    );
            }

            logger.info('[CREATE_RESTAURANT] Initializing default labels', {
                credentialId,
                socialId,
                platformKey,
                restaurantId: upsertedRestaurant._id,
            });
            await this._labelsUseCases.initDefaultLabels(upsertedRestaurant._id);
        }

        await this._usersUseCases.forceExpireAbilitySession(toDbId(userId));
        await this._reportsRepository.addRestaurantForUser(toDbId(userId), upsertedRestaurant);
        await this._restaurantAiSettingRepository.createDefaultRestaurantAiSettings({
            restaurantId: upsertedRestaurant._id.toString(),
            restaurantName: upsertedRestaurant.name,
            regionCode: upsertedRestaurant?.address?.regionCode,
        });
        await this._platformsRepository.upsert({
            filter: {
                key: PlatformKey.MAPSTR,
                restaurantId: upsertedRestaurant._id,
                socialId: upsertedRestaurant.placeId,
            },
            update: {},
            options: { lean: true },
        });

        try {
            await this._yextActivateLocationUseCase.execute(upsertedRestaurant, userId.toString());
        } catch (err: any) {
            if (err.malouErrorCode !== MalouErrorCode.CANNOT_ADD_LOCATION_FOR_BRAND_RESTAURANT) {
                throw err;
            }
        }

        // Hyperline integration - update customer external_id with restaurant ID
        if (hyperlineLocationId && !fromTests) {
            try {
                logger.info('[CREATE_RESTAURANT] Starting Hyperline integration', {
                    restaurantId: upsertedRestaurant._id,
                    hyperlineLocationId,
                });

                await this._createRestaurantWithHyperlineUseCase.execute({
                    restaurantId: upsertedRestaurant._id.toString(),
                    hyperlineCustomerId: hyperlineLocationId,
                    restaurantName: upsertedRestaurant.name,
                });

                logger.info('[CREATE_RESTAURANT] Hyperline integration completed successfully', {
                    restaurantId: upsertedRestaurant._id,
                    hyperlineLocationId,
                });
            } catch (hyperlineError: any) {
                logger.error('[CREATE_RESTAURANT] Hyperline integration failed, restaurant creation will be rolled back', {
                    restaurantId: upsertedRestaurant._id,
                    hyperlineLocationId,
                    error: hyperlineError.message,
                });
                // The CreateRestaurantWithHyperlineUseCase will handle the rollback
                throw hyperlineError;
            }
        }

        logger.info('[CREATE_RESTAURANT] Restaurant created', { credentialId, socialId, restaurantId: upsertedRestaurant._id });
        return this._restaurantsMapper.toCreateRestaurantDto(upsertedRestaurant);
    }
}
