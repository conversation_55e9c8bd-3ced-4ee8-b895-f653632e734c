import { singleton } from 'tsyringe';

import { StoreLocatorOrganizationRestaurantDto } from '@malou-io/package-dto';
import { IRestaurant, PopulateBuilderHelper, toDbId } from '@malou-io/package-models';
import { PlatformKey, RestaurantAttributeValue } from '@malou-io/package-utils';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

type StoreLocatorOrganizationRestaurant = PopulateBuilderHelper<
    IRestaurant,
    [{ path: 'attributeList'; populate: [{ path: 'attribute' }] }]
>;
@singleton()
export class GetStoreLocatorOrganizationRestaurantsUseCase {
    constructor(private readonly _restaurantsRepository: RestaurantsRepository) {}

    async execute(organizationId: string): Promise<StoreLocatorOrganizationRestaurantDto[]> {
        const restaurants = (await this._restaurantsRepository.find({
            filter: {
                organizationId: toDbId(organizationId),
                active: true,
            },
            projection: {
                name: 1,
                internalName: 1,
                address: 1,
                categoryList: 1,
            },
            options: {
                populate: [
                    {
                        path: 'attributeList',
                        populate: [{ path: 'attribute', select: 'attributeId attributeName platformKey' }],
                    },
                ],
                lean: true,
            },
        })) as StoreLocatorOrganizationRestaurant[];

        return restaurants.map((restaurant) => this._toDto(restaurant));
    }

    private _toDto(restaurant: StoreLocatorOrganizationRestaurant): StoreLocatorOrganizationRestaurantDto {
        return {
            id: restaurant._id.toString(),
            name: restaurant.name,
            internalName: restaurant.internalName,
            address: restaurant.address || undefined,
            attributeList: restaurant.attributeList
                ?.filter(
                    (restaurantAttribute) =>
                        restaurantAttribute.attributeValue === RestaurantAttributeValue.YES &&
                        restaurantAttribute.attribute.platformKey === PlatformKey.GMB
                )
                .map((restaurantAttribute) => ({
                    restaurantAttributeId: restaurantAttribute._id.toString(),
                    dbAttributeId: restaurantAttribute.attribute._id.toString(),
                    restaurantId: restaurant._id.toString(),
                    attributeId: restaurantAttribute.attribute.attributeId,
                    platformKey: restaurantAttribute.attribute.platformKey,
                    attributeName: {
                        fr: restaurantAttribute.attribute.attributeName.fr,
                        en: restaurantAttribute.attribute.attributeName.en,
                        es: restaurantAttribute.attribute.attributeName.es,
                        it: restaurantAttribute.attribute.attributeName.it,
                    },
                })),
        };
    }
}
