import 'reflect-metadata';

import ':env';

import fs from 'fs';
import { chunk } from 'lodash';
import path from 'path';
import { container, singleton } from 'tsyringe';

import { DbId, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import { MalouErrorCode, MonthAndYear } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { isFulfilled, isRejected } from ':helpers/utils';
import {
    FailedSearchKeywordsImpressionsResponse,
    SearchKeywordsImpressionsInvocationType,
    SearchKeywordsImpressionsParams,
    SearchKeywordsImpressionsResponse,
    SearchKeywordsImpressionsService,
} from ':microservices/search-keywords-impressions.service';
import KeywordSearchImpressionsRepository from ':modules/keyword-search-impressions/repositories/keyword-search-impressions.repository';
import { BrandKeywordsIdentificationService } from ':modules/keyword-search-impressions/services/brand-keywords-identification/brand-keywords-identification.service';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class ReProcessRestaurantsDataTask {
    readonly errorLogFilePath = path.join(__dirname, 'error.log');
    readonly yearMonths: number[] = [
        202310, 202311, 202312, 202401, 202402, 202403, 202404, 202405, 202406, 202407, 202408, 202409, 202410, 202411, 202412, 202501,
        202502, 202503, 202504, 202505,
    ];

    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _keywordSearchImpressionsRepository: KeywordSearchImpressionsRepository,
        private readonly _organizationRepository: OrganizationsRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _brandKeywordsIdentificationService: BrandKeywordsIdentificationService,
        private readonly _searchKeywordsImpressionsService: SearchKeywordsImpressionsService
    ) {}

    async run() {
        const startTime = Date.now();
        this._createErrorLogFile();

        for (const yearMonth of this.yearMonths) {
            console.log('\n---Re-processing restaurants data for year-month:', yearMonth);

            const restaurantsWithImpressions = await this._getRestaurantsWithImpressions(yearMonth as number);

            const restaurantsWithImpressionsChunks = chunk(restaurantsWithImpressions, 70);

            const promises = restaurantsWithImpressionsChunks.map((chunk) =>
                chunk.map(({ restaurantId, impressionsCount }) => {
                    console.log(`--Processing restaurant ${restaurantId} with ${impressionsCount} impressions`);
                    return this._processRestaurantKeywordImpressions({
                        restaurantId: restaurantId.toString(),
                        monthYearIndex: yearMonth,
                    }).catch((error) => {
                        console.log('Error processing restaurant:', restaurantId, 'for year-month:', yearMonth, error);
                        fs.appendFileSync(
                            this.errorLogFilePath,
                            `Error processing restaurantId: ${restaurantId} for year-month ${yearMonth}: ${JSON.stringify(error)}\n\n`
                        );
                    });
                })
            );

            await Promise.all(promises.flat());
        }
        const endTime = Date.now();
        const duration = endTime - startTime;
        console.log(`Re-processing completed in ${duration / 1000}s`);
    }

    private async _getRestaurantsWithImpressions(yearMonth: number): Promise<{ restaurantId: DbId; impressionsCount: number }[]> {
        return this._keywordSearchImpressionsRepository.aggregate(
            [
                { $match: { yearMonthIndex: yearMonth } },
                { $group: { _id: '$restaurantId', impressionsCount: { $sum: 1 } } },
                { $project: { restaurantId: '$_id', impressionsCount: 1, _id: 0 } },
            ],
            { readPreference: ReadPreferenceMode.SECONDARY }
        );
    }

    private async _processRestaurantKeywordImpressions({
        restaurantId,
        monthYearIndex,
    }: {
        restaurantId: string;
        monthYearIndex: number;
    }): Promise<void> {
        const restaurantData = await this._getRestaurantData(restaurantId);

        const searchKeywordsImpressions = await this._keywordSearchImpressionsRepository.find({
            filter: { restaurantId: toDbId(restaurantId), yearMonthIndex: monthYearIndex },
            options: { readPreference: ReadPreferenceMode.SECONDARY, lean: true, projection: { _id: 0, keywordSearch: 1, value: 1 } },
        });

        const _fromYearMonthIndex = (yearMonthIndex: number): MonthAndYear => {
            return { year: Math.floor(yearMonthIndex / 100), month: yearMonthIndex % 100 };
        };
        const { month, year } = _fromYearMonthIndex(monthYearIndex);

        const params: SearchKeywordsImpressionsParams = {
            month,
            year,
            restaurantData,
            monthImpressionsData: searchKeywordsImpressions.map((impression) => ({
                keywordSearch: impression.keywordSearch,
                value: impression.value,
            })),
            type: SearchKeywordsImpressionsInvocationType.KEYWORD_SEARCH_PROCESSING,
        };

        const CHUNK_SIZE = 1000; // Adjust chunk size as needed
        const monthImpressionsDataChunks = chunk(params.monthImpressionsData, CHUNK_SIZE);

        const promises = monthImpressionsDataChunks.map((monthImpressionsDataChunk) =>
            this._searchKeywordsImpressionsService.processKeywordSearchImpressions({
                month: params.month,
                year: params.year,
                monthImpressionsData: monthImpressionsDataChunk,
                restaurantData: params.restaurantData,
                type: SearchKeywordsImpressionsInvocationType.KEYWORD_SEARCH_PROCESSING,
            })
        );

        const responses = await Promise.allSettled(promises);

        const successfulResponses: SearchKeywordsImpressionsResponse[] = responses
            .filter(isFulfilled)
            .filter((response) => !this._isLambdaResponseInError(response.value))
            .map((response) => response.value.data);

        // if not all promises were successful, return false because we need all data to be saved
        if (successfulResponses.length < promises.length) {
            const failedResponses = responses.filter((response) => isRejected(response) || this._isLambdaResponseInError(response.value));
            throw new MalouError(MalouErrorCode.FAILED_TO_PROCESS_KEYWORD_SEARCH_IMPRESSIONS, {
                metadata: { failedResponses },
            });
        }

        const aggregatedResponses = successfulResponses.reduce(
            (acc, response) => {
                return {
                    month: response.month,
                    year: response.year,
                    monthImpressionsData: acc.monthImpressionsData.concat(response.monthImpressionsData),
                    selectedKeywordsImpressions: [],
                };
            },
            { month: params.month, year: params.year, monthImpressionsData: [], selectedKeywordsImpressions: [] }
        );

        // Upsert the aggregated keyword search impressions
        await this._upsertKeywordSearchImpressions({
            restaurantId,
            month,
            year,
            monthImpressionsData: aggregatedResponses.monthImpressionsData,
        });
    }

    private async _upsertKeywordSearchImpressions({
        restaurantId,
        month,
        year,
        monthImpressionsData,
    }: {
        restaurantId: string;
        month: number;
        year: number;
        monthImpressionsData: SearchKeywordsImpressionsResponse['monthImpressionsData'];
    }): Promise<void> {
        await this._keywordSearchImpressionsRepository.upsertManyKeywordSearchImpressions(
            monthImpressionsData.map((keywordImpression) => ({
                restaurantId,
                month,
                year,
                keywordSearch: keywordImpression.keywordSearch,
                type: keywordImpression.type,
                value: keywordImpression.value,
                relatedKeywordIds: keywordImpression.relatedSelectedKeywords,
            }))
        );
    }

    private async _getRestaurantData(restaurantId: string): Promise<SearchKeywordsImpressionsParams['restaurantData']> {
        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            options: { lean: true },
            projection: { name: 1, organizationId: 1, address: 1, brandKeywords: 1 },
        });
        const organization = await this._organizationRepository.findOneOrFail({
            filter: { _id: restaurant.organizationId },
            options: { lean: true },
            projection: { name: 1 },
        });

        let brandKeywords = restaurant.brandKeywords;
        if (!brandKeywords?.brandGroupKeywords?.length || !brandKeywords?.brandNameKeywords?.length) {
            brandKeywords = await this._brandKeywordsIdentificationService.identifyBrandKeywordsForRestaurant(restaurantId);
        }

        const selectedRestaurantKeywords = await this._restaurantKeywordsRepository.findSelectedRestaurantKeywordsByRestaurantIds([
            restaurantId,
        ]);
        const selectedKeywords = selectedRestaurantKeywords.map((restaurantKeyword) => ({
            id: restaurantKeyword.keywordId,
            text: restaurantKeyword.keyword.text,
        }));
        return {
            formattedAddress: restaurant.address?.formattedAddress ?? '',
            restaurantName: restaurant.name,
            organizationName: organization.name,
            selectedKeywords,
            brandNameKeywords: brandKeywords.brandNameKeywords,
            brandGroupKeywords: brandKeywords.brandGroupKeywords,
        };
    }

    private _createErrorLogFile(): void {
        if (!fs.existsSync(this.errorLogFilePath)) {
            fs.writeFileSync(this.errorLogFilePath, '', 'utf8');
        }
    }

    private _isLambdaResponseInError(
        response: { data: SearchKeywordsImpressionsResponse } | { error: FailedSearchKeywordsImpressionsResponse }
    ): response is { error: FailedSearchKeywordsImpressionsResponse } {
        return 'error' in response;
    }
}

const task = container.resolve(ReProcessRestaurantsDataTask);

task.run()
    .then(() => {
        console.log('ReProcessRestaurantsDataTask - Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('ReProcessRestaurantsDataTask - Task failed', { error });
        process.exit(1);
    });
