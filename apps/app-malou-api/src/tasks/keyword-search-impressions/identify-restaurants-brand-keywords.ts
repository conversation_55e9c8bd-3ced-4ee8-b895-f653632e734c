import 'reflect-metadata';

import ':env';

import { chunk } from 'lodash';
import { container, singleton } from 'tsyringe';

import { BusinessCategory } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { BrandKeywordsIdentificationService } from ':modules/keyword-search-impressions/services/brand-keywords-identification/brand-keywords-identification.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@singleton()
class IdentifyRestaurantsBrandKeywordsTask {
    private readonly _RESTAURANT_CHUNK_SIZE = 25; // nb of parallel lambda calls
    constructor(
        private readonly _restaurantRepository: RestaurantsRepository,
        private readonly _brandKeywordsIdentificationService: BrandKeywordsIdentificationService
    ) {}

    async run() {
        await this._restaurantRepository.updateMany({
            filter: { active: true, type: BusinessCategory.LOCAL_BUSINESS },
            update: { $set: { brandKeywords: { brandNameKeywords: [], brandGroupKeywords: [] } } },
        });

        const restaurants = await this._restaurantRepository.find({
            filter: { active: true, type: BusinessCategory.LOCAL_BUSINESS },
            options: { lean: true },
            projection: { _id: 1 },
        });

        const restaurantIds = restaurants.map((restaurant) => restaurant._id.toString());

        const restaurantChunks = chunk(restaurantIds, this._RESTAURANT_CHUNK_SIZE);

        for (const restaurantChunk of restaurantChunks) {
            await Promise.all(
                restaurantChunk.map((restaurantId) =>
                    this._brandKeywordsIdentificationService.identifyBrandKeywordsForRestaurant(restaurantId)
                )
            );
        }
    }
}

const task = container.resolve(IdentifyRestaurantsBrandKeywordsTask);

task.run()
    .then(() => {
        logger.info('IdentifyRestaurantsBrandKeywordsTask - Task completed');
        process.exit(0);
    })
    .catch((error) => {
        logger.error('IdentifyRestaurantsBrandKeywordsTask - Task failed', { error });
        process.exit(1);
    });
