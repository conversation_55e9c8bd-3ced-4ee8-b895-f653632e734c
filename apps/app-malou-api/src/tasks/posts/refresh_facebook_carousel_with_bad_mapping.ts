import 'reflect-metadata';

// Required for tsyringe
import ':env';

import { autoInjectable, container } from 'tsyringe';

import { IPost } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { RefreshSocialPostUseCase } from ':modules/posts/v2/use-cases/refresh-social-post/refresh-social-post.use-case';
import ':plugins/db';

@autoInjectable()
class RefreshFacebookCarouselWithBadMappingTask {
    constructor(
        private readonly _refreshSocialPostUseCase: RefreshSocialPostUseCase,
        private readonly _postsRepository: PostsRepository
    ) {}
    async execute() {
        const startDate = new Date('2025-01-01');
        const endDate = new Date();

        const cursor = this._postsRepository.model
            .aggregate([
                {
                    $match: {
                        published: PostPublicationStatus.PUBLISHED,
                        key: PlatformKey.FACEBOOK,
                        $expr: {
                            $lt: [{ $size: { $ifNull: ['$socialAttachments', []] } }, { $size: { $ifNull: ['$attachments', []] } }],
                        },
                        plannedPublicationDate: { $gte: startDate, $lt: endDate },
                    },
                },
            ])
            .cursor();

        let i = 0;

        await cursor.eachAsync(
            async (posts: IPost[]) => {
                for (const post of posts) {
                    await this._refreshSocialPostUseCase.execute({ id: post._id.toString() });
                }
                i += posts.length;
                console.log(`Updated ${i} posts`);
            },
            { batchSize: 10 }
        );
    }
}

const task = container.resolve(RefreshFacebookCarouselWithBadMappingTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
