// db connexion + load models
import 'reflect-metadata';

import ':env';

import ':di';
import readline from 'readline';
import { container } from 'tsyringe';
import { argv } from 'yargs';

import { toDbId } from '@malou-io/package-models';

import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import RestaurantsUseCases from ':modules/restaurants/restaurants.use-cases';
import ':plugins/db';

const restaurantsUseCases = container.resolve(RestaurantsUseCases);
const restaurantsRepository = container.resolve(RestaurantsRepository);

const { restaurantId } = argv as { restaurantId?: string };
if (!restaurantId) {
    console.warn(`Need restaurantId.
    Example NODE_ENV=local pnpm run delete-restaurant --restaurantId=5fb65728d17f75a8a97b6ec7`);
    process.exit(0);
}

const rl = readline.createInterface({ input: process.stdin, output: process.stdout });

const createQuestion = (question) =>
    new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });

const main = async () => {
    try {
        const restaurant = await restaurantsRepository.findOne({ filter: { _id: toDbId(restaurantId) } });
        if (!restaurant) {
            console.warn(`Restaurant not found.`);
            process.exit(0);
        }
        const answer = await createQuestion(`Are you sure you want to delete restaurant ${restaurant.name} ? (N/y) `);
        if (answer === 'y') {
            await restaurantsUseCases.deleteRestaurant(toDbId(restaurantId));
            console.log('Restaurant deleted');
        } else {
            console.log('Stopping process');
        }
        process.exit(0);
    } catch (error) {
        console.warn('err >>', error);
        process.exit(1);
    }
};

main()
    .then(() => {
        console.log('Done');
        process.exit(0);
    })
    .catch((err) => {
        console.warn('Error: ', err);
        process.exit(0);
    });
