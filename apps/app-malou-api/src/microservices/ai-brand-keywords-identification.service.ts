import { singleton } from 'tsyringe';

import { Config } from ':config';
import { GenericAiService, GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { SearchKeywordsImpressionsInvocationType } from ':microservices/search-keywords-impressions.service';

export type BrandKeywordsIdentificationPayload = {
    restaurantData: {
        restaurantName: string;
        organizationName: string;
        formattedAddress: string;
        locality: string;
        country: string;
    };
    type: SearchKeywordsImpressionsInvocationType.BRAND_KEYWORDS_IDENTIFICATION;
};

export type BrandKeywordsIdentificationResponse = {
    brandNameKeywords: string[];
    brandGroupKeywords: string[];
};

@singleton()
export class AiBrandKeywordsIdentificationService {
    async processBrandKeywordsIdentification(
        payload: BrandKeywordsIdentificationPayload
    ): Promise<GenericAiServiceResponseType<BrandKeywordsIdentificationResponse>> {
        const AiService = new GenericAiService<BrandKeywordsIdentificationPayload, BrandKeywordsIdentificationResponse>({
            lambdaUrl: Config.services.searchKeywordsImpressions.functionName,
        });
        return AiService.generateCompletion(payload);
    }
}
