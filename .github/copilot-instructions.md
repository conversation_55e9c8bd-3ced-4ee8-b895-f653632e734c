# Project coding standards

- This file presents a set of rules by category to use when writing code.

## Typescript guidelines

- Write code that is readable, understandable, and maintainable for future readers.
- Prioritize clarity to make reading, understanding, and modifying code easier.
- Regularly review and refactor code to improve structure, readability, and maintainability. Always leave the codebase cleaner than you found it.

## Conditional Encapsulation

- One way to improve the readability and clarity of functions is to encapsulate nested if/else statements into other functions.
- Encapsulating such logic into a function with a descriptive name clarifies its purpose and simplifies code comprehension.

## Single Responsibility Principle

- Follow the single responsibility principle (SRP), which means that a function should have one purpose and perform it effectively.
- Write short functions that only do one thing.
- If a function becomes too long or complex, consider breaking it into smaller, more manageable functions.

## Dry principle

- Follow the DRY (Don't Repeat Yourself) Principle and Avoid Duplicating Code or Logic.
- Avoid writing the same code more than once. Instead, reuse your code using functions, classes, modules, libraries, or other abstractions.
- Modify code in one place if you need to change or update it.

## Error Handling

- Use try/catch blocks for async operations.
- Always log errors with contextual information.

## Naming

- Choose names for variables, functions, and classes that reflect their purpose and behavior.
- A name should tell you why it exists, what it does, and how it is used. If a name requires a comment, then the name does not reveal its intent.
- Use specific names that provide a clearer understanding of what the variables represent and how they are used.

## Comments

- Use comments sparingly, and when you do, make them meaningful.
- Don't comment on obvious things. Excessive or unclear comments can clutter the codebase and become outdated.
- Use comments to convey the "why" behind specific actions or explain unusual behavior and potential pitfalls.
- Provide meaningful information about the function's behavior and explain unusual behavior and potential pitfalls.

## Other

- Never use apologies.
- Don't invent changes other than what's explicitly requested.
- Don't summarize changes made.
- Don't suggest updates or changes to files when there are no actual modifications needed.
- Don't remove unrelated code or functionalities. Pay attention to preserving existing structures.
- Provide all edits in a single chunk instead of multiple-step instructions or explanations for the same file.

## Translation Guidelines

When working with i18n translations in this project:

### Reading Translation Files

- Only read the lines specified in the prompt in all the i18n files to avoid high demand on the LLM.
- Translation files are located in `/apps/app-malou-web/src/assets/i18n/`
- Available languages: French (fr.json), English (en.json), Spanish (es.json), Italian (it.json)

### Translation Key Structure

- Use hierarchical keys following the existing structure: `module.section.key`
- For store_locator module, structure should be: `store_locator.section.subsection.key`
- French text is the source language - translate from French to other languages

### Adding New Translations

1. Extract hardcoded French text from HTML/TypeScript files
2. Create meaningful translation keys following the established hierarchy
3. Add translations to all language files (fr.json, en.json, es.json, it.json)
4. Update HTML templates to use `| translate` pipe
5. Ensure TranslateModule is imported in TypeScript components

### Translation Implementation

- Always import and include TranslateModule in component imports array
- Replace hardcoded text with translation keys using Angular's translate pipe: `{{ 'key' | translate }}`
- For component properties, use: `[property]="'key' | translate"`
- Maintain consistent naming conventions across all language files

### File Organization

- Keep translation keys organized by feature modules
- Group related translations under common parent keys
- Use descriptive key names that reflect the content purpose
