import { z } from 'zod';

import { objectIdValidator } from '../utils/validators';

export const getStoreLocatorOrganizationKeywordsBodyValidator = z.object({
    restaurantIds: z.array(objectIdValidator),
});

export type GetStoreLocatorOrganizationKeywordsBodyDto = z.infer<typeof getStoreLocatorOrganizationKeywordsBodyValidator>;

const storeLocatorOrganizationKeywordDto = z.object({
    restaurantKeywordId: objectIdValidator,
    keywordId: objectIdValidator,
    restaurantId: objectIdValidator,
    text: z.string(),
});

export type StoreLocatorOrganizationKeywordDto = z.infer<typeof storeLocatorOrganizationKeywordDto>;
