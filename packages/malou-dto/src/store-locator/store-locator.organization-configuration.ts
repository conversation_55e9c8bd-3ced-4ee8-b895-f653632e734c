import { z } from 'zod';

import { StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { objectIdValidator, urlValidator } from '../utils';

export const getStoreLocatorOrganizationConfigurationValidator = z.object({
    cloudfrontDistributionId: z.string(),
    organizationName: z.string(),
    baseUrl: z.string(),
    tailwindConfig: z.string(),
    tailwindClassesMap: z.string(),
    favIconUrl: urlValidator(),
});

export type GetStoreLocatorOrganizationConfigurationDto = z.infer<typeof getStoreLocatorOrganizationConfigurationValidator>;

// -------------------------------------------------------------------------------

export const getStoreLocatorOrganizationStyleConfigurationValidator = z.object({
    colors: z.array(
        z.object({
            class: z.string(),
            value: z.string(),
        })
    ),
    fonts: z.array(
        z.object({
            class: z.string(),
            src: z.string(),
            weight: z.string().optional(),
            style: z.string().optional(),
        })
    ),
    pages: z.record(z.string(), z.any()),
});

export type GetStoreLocatorOrganizationStyleConfigurationDto = z.infer<typeof getStoreLocatorOrganizationStyleConfigurationValidator>;

// -------------------------------------------------------------------------------

export const updateOrganizationConfigurationAiSettingsParamsValidator = z.object({
    organizationId: objectIdValidator,
});

export type UpdateOrganizationConfigurationAiSettingsParamsDto = z.infer<typeof updateOrganizationConfigurationAiSettingsParamsValidator>;

export const updateOrganizationConfigurationAiSettingsBodyValidator = z.object({
    aiSettings: z.object({
        tone: z.array(z.string()).optional(),
        languageStyle: z.nativeEnum(StoreLocatorAiSettingsLanguageStyle).optional(),
        restaurantAttributeIds: z.array(objectIdValidator).optional(),
        restaurantKeywordIds: z.array(objectIdValidator).optional(),
        specialAttributes: z
            .array(
                z.object({
                    restaurantId: objectIdValidator,
                    text: z.string(),
                })
            )
            .optional(),
    }),
});

export type UpdateOrganizationConfigurationAiSettingsBodyDto = z.infer<typeof updateOrganizationConfigurationAiSettingsBodyValidator>;

// -------------------------------------------------------------------------------
