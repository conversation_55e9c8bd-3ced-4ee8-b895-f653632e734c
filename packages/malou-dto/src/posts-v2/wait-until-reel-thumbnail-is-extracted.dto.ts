import z from 'zod';

import { objectIdValidator } from '../utils';

export const waitUntilReelThumbnailIsExtractedParamsValidator = z.object({
    postId: objectIdValidator,
});

export type WaitUntilReelThumbnailIsExtractedParamsDto = z.infer<typeof waitUntilReelThumbnailIsExtractedParamsValidator>;

export type WaitUntilReelThumbnailIsExtractedResponseDto = {
    /** ID of a Media document */
    reelThumbnailId: string | null;
};
