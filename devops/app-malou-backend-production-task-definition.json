{"containerDefinitions": [{"command": ["node", "dist/src/server"], "dependsOn": [{"condition": "START", "containerName": "open-telemetry-collector"}], "environment": [{"name": "NODE_OPTIONS", "value": "--max-old-space-size=6144"}, {"name": "GOOGLE_REVIEWS_SUBSCRIPTION_NAME", "value": "projects/malou-product/subscriptions/gmb_reviews-sub"}, {"name": "START_PUBSUB_SUBSCRIPTION", "value": "true"}, {"name": "BN_PROJECT_ID", "value": "malou-product"}, {"name": "BN_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "PUSHER_APP_ID", "value": "*******"}, {"name": "PUSHER_KEY", "value": "fefb73f5df41cc7faa1d"}, {"name": "PUSHER_CLUSTER", "value": "eu"}, {"name": "PASSWORD_CRYPT_KEY", "value": "dragNdr0p"}, {"name": "BRICKS_CACHING_TTL", "value": "604800"}, {"name": "AWS_BUCKET", "value": "malou-production"}, {"name": "BASE_URL", "value": "https://app.malou.io"}, {"name": "FB_APP_ID", "value": "1377266962404541"}, {"name": "FB_REDIRECT_URI", "value": "https://app.api.malou.io/api/v1/facebook/oauth/callback/"}, {"name": "TIKTOK_CLIENT_ID", "value": "awbswt2qgd11miy7"}, {"name": "TIKTOK_REDIRECT_URI", "value": "https://app.api.malou.io/api/v1/tiktok/oauth/callback"}, {"name": "FOURSQUARE_CLIENT_ID", "value": "************************************************"}, {"name": "FOURSQUARE_REDIRECT_URI", "value": ""}, {"name": "GMB_CLIENT_ID", "value": "712726183109-u1ieku54ejo3tpf9mu22c2ogdp64i9ei.apps.googleusercontent.com"}, {"name": "GMB_REDIRECT_URIS", "value": "https://app.api.malou.io/api/v1/google/oauth2callback"}, {"name": "NODE_ENV", "value": "production"}, {"name": "YELP_CLIENT_ID", "value": "IEIf37zltcqlYia_9yfjCQ"}, {"name": "AWS_KEY", "value": "AKIAXP46YDWPGGPZDDA3"}, {"name": "MAILGUN_DOMAIN", "value": "mail.app.malou.io"}, {"name": "MAILGUN_HOST", "value": "api.eu.mailgun.net"}, {"name": "PLATFORMS_SCRAPPER_URL", "value": "https://8457xgi890.execute-api.eu-west-3.amazonaws.com/production/platforms-scrapper"}, {"name": "PYTHON_CRAWLER_URL", "value": "https://8457xgi890.execute-api.eu-west-3.amazonaws.com/development/python-crawler"}, {"name": "KEYWORDS_GENERATOR_URL", "value": "https://dzzqxto6n1.execute-api.eu-west-3.amazonaws.com/production/keywordsGenerator"}, {"name": "FB_API_VERSION", "value": "v20.0"}, {"name": "FIXED_IP_CALLER_URL", "value": "https://l86x12pjii.execute-api.eu-west-3.amazonaws.com/development/crawl/node/us-static"}, {"name": "HASHTAGS_GENERATOR_URL", "value": "https://dzzqxto6n1.execute-api.eu-west-3.amazonaws.com/production/hashtagsGenerator"}, {"name": "START_SQS_CONSUMER", "value": "false"}, {"name": "SEND_EMAIL", "value": "true"}, {"name": "SCRAP_PAGES_JAUNES", "value": "true"}, {"name": "FETCH_RANKINGS", "value": "true"}, {"name": "ADMIN_APP_EMAIL", "value": "<EMAIL>"}, {"name": "ELASTICACHE_URI", "value": "ec-redis-prod.hnqdvp.ng.0001.euw3.cache.amazonaws.com"}, {"name": "ELASTICACHE_PORT", "value": "6379"}, {"name": "UBEREATS_REDIRECT_URI", "value": "https://app.api.malou.io/api/v1/ubereats/oauth/callback"}, {"name": "UBEREATS_CLIENT_ID", "value": "wQsVVx8aEPW7uHi1Zql8Z1uPU_345FWO"}, {"name": "REVIEWS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_collect_reviews_production"}, {"name": "SCRAPPER_API_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_scrapper_api_production"}, {"name": "PUBLISH_POST_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_publish_post_production"}, {"name": "REVIEW_BOOSTER_SNS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_review_booster_sns_production"}, {"name": "FETCH_KEYWORDS_VOLUME_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_keywords_volume"}, {"name": "FETCH_KEYWORDS_VOLUME_QUEUE_ARN", "value": "arn:aws:sqs:eu-west-3:515192135070:malou_sqs_fetch_keywords_volume"}, {"name": "BASE_API_URL", "value": "https://app.api.malou.io/api/v1"}, {"name": "BASE_API_MALOUPE_URL", "value": "https://app.api.malou.io/api/maloupe"}, {"name": "PUPPETEER_SERVICE_ARN", "value": "arn:aws:lambda:eu-west-3:515192135070:function:puppeteerService"}, {"name": "BM_PARTNER_NAME", "value": "<PERSON><PERSON>"}, {"name": "BM_PARTNER_EMAIL_ADDRESS", "value": "<EMAIL>"}, {"name": "BM_BRAND_CONTACT_NAME", "value": "<PERSON><PERSON>"}, {"name": "BM_BRAND_CONTACT_EMAIL_ADDRESS", "value": "<EMAIL>"}, {"name": "BM_BRAND_WEBSITE_URL", "value": "https://malou.io/"}, {"name": "BM_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "SENTRY_API_PROJECT_URI", "value": "https://<EMAIL>/5799418"}, {"name": "MEDIA_CONVERT_ENDPOINT", "value": "https://xpxxifqxa.mediaconvert.eu-west-3.amazonaws.com"}, {"name": "AWS_MEDIA_CONVERT_ROLE", "value": "arn:aws:iam::515192135070:role/service-role/MediaConvert_Default_Role"}, {"name": "MEDIA_CONVERT_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_convert_media_production"}, {"name": "V3_BASE_URL", "value": "https://v3.app.malou.io"}, {"name": "PLATFORMS_SCRAPPER_FUNCTION_NAME", "value": "platformsScrapperProduction"}, {"name": "KEYWORDS_GENERATOR_FUNCTION_NAME", "value": "keywordsGeneratorProduction"}, {"name": "V2_BASE_URL", "value": "https://v2.app.malou.io"}, {"name": "FIREBASE_PROJECT_ID", "value": "malou-1555310857472"}, {"name": "FIREBASE_CLIENT_EMAIL", "value": "<EMAIL>"}, {"name": "TRANSLATOR_FUNCTION_NAME", "value": "serverlessTranslator"}, {"name": "CLOUDINARY_CLOUD_NAME", "value": "dtjj53vxe"}, {"name": "CLOUDINARY_API_KEY", "value": "989924112896137"}, {"name": "THUMBNAIL_GENERATOR_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_thumbnail_queue_production"}, {"name": "SLACK_ALERTS_WEBHOOK_URL", "value": "https://hooks.slack.com/triggers/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"}, {"name": "SLACK_APP_ALERTS_WEBHOOK_URL", "value": "*******************************************************************************"}, {"name": "KEYWORDS_SCORE_FUNCTION_NAME", "value": "keywordsScoreProduction"}, {"name": "EXPERIMENTATION_APP_URL", "value": "https://experimentation.malou.io:3300"}, {"name": "EXPERIMENTATION_APP_CLIENT_KEY", "value": "sdk-si1NwbCfb8xdbd2"}, {"name": "REVIEWS_SEMANTIC_ANALYSIS_OVERVIEW_FUNCTION_NAME", "value": "reviewsSemanticAnalysisOverviewProduction"}, {"name": "KEYWORDS_GENERATOR_FUNCTION_NAME_V2", "value": "keywordsGeneratorV2Production"}, {"name": "AI_TEXT_GENERATION_SERVICE_FUNCTION_NAME", "value": "aiTextGenerationProduction"}, {"name": "AI_HASHTAG_GEN_SERVICE_FUNCTION_NAME", "value": "serverlessHashtagGeneratorProduction"}, {"name": "YEXT_API_BASE_URL", "value": "https://api.yextapis.com/v2"}, {"name": "YEXT_API_VERSION", "value": "20240424"}, {"name": "YEXT_KNOWLEDGE_ENGINE_STARTER_SKU", "value": "GLOBAL-00000292"}, {"name": "YEXT_US_KNOWLEDGE_ENGINE_STARTER_SKU", "value": "LC-00000019"}, {"name": "YEXT_US_MENU_LISTING_SKU", "value": "GLOBAL-00000397"}, {"name": "APPLE_BUSINESS_CONNECT_API_URL", "value": "https://businessconnect.apple.com"}, {"name": "APPLE_BUSINESS_CONNECT_COMPANY_ID", "value": "1501513140663465984"}, {"name": "APPLE_BUSINESS_CONNECT_CLIENT_ID", "value": "14d6723e-fed3-b800-1500-88f521ef2000"}, {"name": "TEXT_TRANSLATOR_FUNCTION_NAME", "value": "serverlessTextTranslatorProduction"}, {"name": "SIMILAR_RESTAURANTS_FUNCTION_NAME", "value": "similarRestaurantsIdentificationProduction"}, {"name": "RESY_API_KEY", "value": "VbWk7s3L4KiK5fzlO7JD3Q5EYolJI7n5"}, {"name": "CREATE_MESSAGE_QUEUES_MONTHLY_SAVE_ROI_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_queues_monthly_save_roi_insights_production"}, {"name": "MONTHLY_ROI_SAVE_INSIGHTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_save_roi_insights_production"}, {"name": "CREATE_MESSAGES_MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_messages_monthly_update_similar_restaurants_production"}, {"name": "MONTHLY_UPDATE_SIMILAR_RESTAURANTS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_update_similar_restaurants_production"}, {"name": "OPEN_TELEMETRY_COLLECTOR_HOST", "value": "localhost"}, {"name": "OTEL_NODE_RESOURCE_DETECTORS", "value": "env,host"}, {"name": "DIAGNOSTIC_KEYWORDS_GENERATOR_FUNCTION_NAME", "value": "serverlessKeywordDiagnosisProduction"}, {"name": "CREATE_NEW_REVIEWS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_send_new_reviews_notification_production"}, {"name": "MALOUPE_URL", "value": "https://diagnostic.malou.io"}, {"name": "CREATE_POST_ERROR_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_post_error_notification_production"}, {"name": "CREATE_COMMENTS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_comments_notification_production"}, {"name": "CREATE_MENTIONS_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_mentions_notification_production"}, {"name": "CREATE_MESSAGE_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_message_notification_production"}, {"name": "AI_MEDIA_DESCRIPTION_GENERATION_SERVICE_FUNCTION_NAME", "value": "serverlessAiImageAnalysisProduction"}, {"name": "CREATE_PLATFORM_DISCONNECTED_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_platform_disconnected_notification_production"}, {"name": "AI_TEXT_DUPLICATION_FUNCTION_NAME", "value": "serverlessAiPostDuplicationProduction"}, {"name": "CREATE_INFO_UPDATE_ERROR_NOTIFICATION_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_create_info_update_error_notification_production"}, {"name": "UPDATE_REVIEW_RELEVANT_BRICKS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_update_review_relevant_bricks_production"}, {"name": "AI_REVIEWS_FUNCTION_NAME", "value": "serverlessAiReviewsProduction"}, {"name": "AI_KEYWORDS_BREAKDOWN_SERVICE_FUNCTION_NAME", "value": "serverlessKeywordsBreakdownProduction"}, {"name": "FETCH_REVIEW_SEMANTIC_ANALYSIS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_review_semantic_analysis_production"}, {"name": "FETCH_REVIEW_SEMANTIC_ANALYSIS_FIFO_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_fetch_review_semantic_analysis_production.fifo"}, {"name": "NEW_GMAPS_API_ROLLOUT", "value": "0"}, {"name": "AI_SEMANTIC_ANALYSIS_FUNCTION_NAME", "value": "serverlessAiSemanticAnalysisProduction"}, {"name": "MONTHLY_SAVE_KEYWORD_SEARCH_IMPRESSIONS_QUEUE_URL", "value": "https://sqs.eu-west-3.amazonaws.com/515192135070/malou_sqs_monthly_save_keyword_search_impressions_production"}, {"name": "HYPERLINE_API_BASE_URL", "value": "https://api.hyperline.co/v1"}], "image": "515192135070.dkr.ecr.eu-west-3.amazonaws.com/malou/app-malou-api:production", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/app-malou-backend-production", "awslogs-region": "eu-west-3", "awslogs-stream-prefix": "ecs"}}, "memoryReservation": 6144, "name": "app-malou-api", "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "secrets": [{"name": "MAPSTR_BEARER_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/mapstr_bearer_token-NQsM5h"}, {"name": "BN_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/bn_private_key-ddIVxX"}, {"name": "PUSHER_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/pusher_secret-Xfe11X"}, {"name": "PLATFORMS_SCRAPPER_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/platforms_scrapper_authorization-zX8Zme"}, {"name": "NODE_CRAWLER_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/node_crawler_api_key-4f48j2"}, {"name": "KEYWORDS_GENERATOR_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/keywords_generator_authorization-LJgrm3"}, {"name": "AWS_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/aws_key-FiDiDr"}, {"name": "FB_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/fb_client_secret-kJfVam"}, {"name": "TIKTOK_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/tiktok_client_secret-XF21nO"}, {"name": "FOURSQUARE_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:staging/app-malou-api/foursquare_client_secret-NxJswl"}, {"name": "GMB_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/gmb_client_secret-dR5QKV"}, {"name": "MONGODB_URI", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/mongodb_uri-e9qBBh"}, {"name": "PASSPORT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/passport_secret-xcdX5y"}, {"name": "GMAPS_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app_malou_api/gmaps_apikey-kq9giw"}, {"name": "YELP_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/yelp_api_key-zsF3AD"}, {"name": "APPLE_BUSINESS_CONNECT_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/apple_business_connect_client_secret-fSTCfp"}, {"name": "ENCRYPT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/encrypt_secret-n7UJhm"}, {"name": "JOBS_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/jobs_key-uZF0KH"}, {"name": "MAILGUN_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/mailgun_api_key-fYk79G"}, {"name": "FB_WEBHOOK_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/facebook_webhook_token-kCDLHN"}, {"name": "MALOU_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/malou_api_key-pXJzzz"}, {"name": "HASHTAGS_GENERATOR_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/hashtags_generator_api_key-wmSrC5"}, {"name": "KEYWORDS_GENERATOR_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/hashtags_generator_api_key-wmSrC5"}, {"name": "FB_APP_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app_malou_api/fb_app_token-QmVFwm"}, {"name": "PUPPETEER_SERVICE_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/puppeteer_service_authorization-1JR6Ko"}, {"name": "UBEREATS_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/ubereats_client_secret-Q1Egxo"}, {"name": "BM_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/bm_private_key-RC3kOq"}, {"name": "GITHUB_APP_CLIENT_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/github_app_client_secret-r9TX7r"}, {"name": "GITHUB_APP_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/github_app_private_key-eqMAcS"}, {"name": "MONGODB_AGENDA_URI", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/mongodb_agenda_uri-ercdTr"}, {"name": "SCRAPPER_PROXY_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/scrapper_proxy_token-5yB8GQ"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/openai_api_key-fdAp5K"}, {"name": "FIREBASE_PRIVATE_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/firebase_private_key-VY8mSS"}, {"name": "TRANSLATOR_AUTHORIZATION", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/translator_authorization-Xc16jf"}, {"name": "CLOUDINARY_API_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/cloudinary_api_secret-8INqaM"}, {"name": "FRONT_CHAT_USER_VERIFICATION_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:development/app-malou-api/front_chat_user_verification_secret-Wsn6Cd"}, {"name": "KEYWORD_TOOL_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/keyword_tool_api_key-aAY5JG"}, {"name": "MALOUPE_GMAPS_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/maloupe/google_api_key-C6KqVL"}, {"name": "BRIGHT_DATA_RESIDENTIAL_PROXY_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/bright_data_residential_proxy_password-dVJMvZ"}, {"name": "BRIGHT_DATA_ISP_PROXY_PASSWORD", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/bright_data_isp_proxy_password-HSTEKW"}, {"name": "FRONT_CHAT_USER_VERIFICATION_SECRET_EN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/front_chat_user_verification_secret_en-2VcuC6"}, {"name": "SLACK_TECH_BOT_TOKEN", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/slack_tech_bot_token-hDCH1C"}, {"name": "SLACK_TECH_BOT_SIGNING_SECRET", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:shared/slack_tech_bot_signing_secret-WyNGQ4"}, {"name": "JIMO_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/jimo_api_key-VqO7XY"}, {"name": "HYPERLINE_API_KEY", "value": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/hyperline_api_key-STAXg9"}, {"name": "YEXT_API_KEY", "valueFrom": "arn:aws:secretsmanager:eu-west-3:515192135070:secret:production/app-malou-api/yext_api_key-e19Dit"}]}, {"environment": [{"name": "OTEL_RESOURCE_ATTRIBUTES", "value": "service.name=api,environment=production,deployment.environment=production"}], "essential": true, "image": "amazon/aws-otel-collector", "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/open-telemetry-collector-production", "awslogs-region": "eu-west-3", "awslogs-stream-prefix": "ecs"}}, "name": "open-telemetry-collector", "secrets": [{"name": "AOT_CONFIG_CONTENT", "valueFrom": "otel-collector-config"}]}], "cpu": "2048", "executionRoleArn": "arn:aws:iam::515192135070:role/ecsTaskExecutionRole", "family": "app-malou-backend-production", "memory": "8192", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "taskRoleArn": "arn:aws:iam::515192135070:role/ECSTaskRole"}